import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:ehabitat_mobile/core/theme/colors.dart';
import 'package:ehabitat_mobile/presentation/widgets/gradient_background.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Stack(
        children: [
          // Gradient background
          const GradientBackground(),

          // Main content
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Profile header with large avatar
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.fromLTRB(20.w, 60.h, 20.w, 30.h),
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                          Container(
                            width: 120.w,
                            height: 120.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppColors.gold,
                                width: 3,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.black.withOpacity(0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: ClipOval(
                              child: Container(
                                color: isDark
                                    ? AppColors.gray.withOpacity(0.2)
                                    : const Color(0xFFF5F5F5),
                                child: Stack(
                                  children: [
                                    Center(
                                      child: Icon(
                                        Icons.person,
                                        size: 60.sp,
                                        color: isDark
                                            ? AppColors.white.withOpacity(0.3)
                                            : AppColors.gray.withOpacity(0.3),
                                      ),
                                    ),
                                    // We'll only show the actual image if we have a valid URL
                                    if (_hasValidProfileImage())
                                      CachedNetworkImage(
                                        imageUrl: _getProfileImageUrl(),
                                        fit: BoxFit.cover,
                                        errorWidget: (context, url, error) =>
                                            const SizedBox(),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.all(8.w),
                            decoration: BoxDecoration(
                              color: AppColors.gold,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                width: 3,
                              ),
                            ),
                            child: Icon(
                              Icons.camera_alt,
                              size: 20.sp,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 20.h),
                      Text(
                        "John Doe",
                        style: Theme.of(context)
                            .textTheme
                            .headlineMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        "Property Manager",
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.gray,
                                ),
                      ),
                      SizedBox(height: 16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildStatItem(context, "15", "Properties"),
                          Container(
                            height: 30.h,
                            width: 1,
                            color: AppColors.gray.withOpacity(0.3),
                            margin: EdgeInsets.symmetric(horizontal: 20.w),
                          ),
                          _buildStatItem(context, "127", "Tenants"),
                          Container(
                            height: 30.h,
                            width: 1,
                            color: AppColors.gray.withOpacity(0.3),
                            margin: EdgeInsets.symmetric(horizontal: 20.w),
                          ),
                          _buildStatItem(context, "98%", "Occupancy"),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Profile sections
              SliverToBoxAdapter(
                child: Container(
                  margin: EdgeInsets.all(20.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withOpacity(0.05),
                        blurRadius: 20,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      _buildProfileSection(
                        context,
                        "Personal Information",
                        Icons.person_outline,
                        [
                          _buildInfoItem(context, "Email",
                              "<EMAIL>", Icons.email_outlined),
                          _buildInfoItem(context, "Phone", "****** 567 8900",
                              Icons.phone_outlined),
                          _buildInfoItem(context, "Location",
                              "San Francisco, CA", Icons.location_on_outlined),
                        ],
                      ),
                      _buildDivider(),
                      _buildProfileSection(
                        context,
                        "Account Settings",
                        Icons.settings_outlined,
                        [
                          _buildSettingsItem(context, "Edit Profile",
                              Icons.edit_outlined, () {}),
                          _buildSettingsItem(context, "Notification Settings",
                              Icons.notifications_outlined, () {}),
                          _buildSettingsItem(context, "Security & Privacy",
                              Icons.security_outlined, () {}),
                          _buildSettingsItem(context, "Payment Methods",
                              Icons.payment_outlined, () {}),
                        ],
                      ),
                      _buildDivider(),
                      _buildProfileSection(
                        context,
                        "Support & Help",
                        Icons.help_outline,
                        [
                          _buildSettingsItem(context, "Help Center",
                              Icons.help_outline, () {}),
                          _buildSettingsItem(context, "Contact Support",
                              Icons.headset_mic_outlined, () {}),
                          _buildSettingsItem(context, "Terms of Service",
                              Icons.description_outlined, () {}),
                          _buildSettingsItem(context, "Privacy Policy",
                              Icons.privacy_tip_outlined, () {}),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Logout button
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: ElevatedButton(
                    onPressed: () {},
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.burgundy.withOpacity(0.1),
                      foregroundColor: AppColors.burgundy,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.logout, size: 24.sp),
                        SizedBox(width: 8.w),
                        Text(
                          "Logout",
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Bottom padding
              SliverPadding(padding: EdgeInsets.only(bottom: 20.h)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.gold,
              ),
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.gray,
              ),
        ),
      ],
    );
  }

  Widget _buildProfileSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 24.sp,
                color: AppColors.gold,
              ),
              SizedBox(width: 12.w),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(10.w),
            decoration: BoxDecoration(
              color: AppColors.gold.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              icon,
              size: 20.sp,
              color: AppColors.gold,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.gray,
                      ),
                ),
                SizedBox(height: 2.h),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(10.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(10.w),
              decoration: BoxDecoration(
                color: AppColors.gold.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Icon(
                icon,
                size: 20.sp,
                color: AppColors.gold,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 24.sp,
              color: AppColors.gray,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      color: AppColors.gray.withOpacity(0.1),
      thickness: 1,
      height: 1,
    );
  }

  bool _hasValidProfileImage() {
    // TODO: Replace with actual logic to check if user has a profile image
    return false;
  }

  String _getProfileImageUrl() {
    // TODO: Replace with actual logic to get user's profile image URL
    return '';
  }
}
