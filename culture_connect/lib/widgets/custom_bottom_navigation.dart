import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A custom bottom navigation bar with Riverpod integration.
///
/// This component combines the best features of both navigation implementations:
/// - Riverpod state management for navigation
/// - Animated transitions between states
/// - Multiple style options (standard, fancy with center button)
/// - Customizable appearance
class CustomBottomNavigation extends ConsumerWidget {
  /// The style of the bottom navigation bar
  final BottomNavStyle style;

  /// Whether to show labels for the navigation items
  final bool showLabels;

  /// The height of the navigation bar (excluding safe area)
  final double height;

  /// The size of the icons
  final double iconSize;

  /// The font size for selected items
  final double selectedFontSize;

  /// The font size for unselected items
  final double unselectedFontSize;

  const CustomBottomNavigation({
    super.key,
    this.style = BottomNavStyle.standard,
    this.showLabels = true,
    this.height = 60,
    this.iconSize = 24,
    this.selectedFontSize = 12,
    this.unselectedFontSize = 10,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(navigationProvider);

    // Use the appropriate style
    if (style == BottomNavStyle.fancy) {
      return _buildFancyNavigation(context, ref, currentIndex);
    }

    return Container(
      height: height + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: navigationDestinations.map((destination) {
              final isSelected = currentIndex == destination.item;
              return Expanded(
                child: _ModernNavigationItem(
                  destination: destination,
                  isSelected: isSelected,
                  showLabel: showLabels,
                  iconSize: iconSize,
                  selectedFontSize: selectedFontSize,
                  unselectedFontSize: unselectedFontSize,
                  onTap: () {
                    HapticFeedback.lightImpact();
                    ref
                        .read(navigationProvider.notifier)
                        .setNavigationItem(destination.item);
                  },
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildFancyNavigation(
    BuildContext context,
    WidgetRef ref,
    NavigationItem currentIndex,
  ) {
    return Container(
      height: height + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Bottom nav items
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildModernFancyNavItem(
                    context: context,
                    ref: ref,
                    destination: navigationDestinations[0],
                    isSelected: currentIndex == NavigationItem.home,
                  ),
                  _buildModernFancyNavItem(
                    context: context,
                    ref: ref,
                    destination: navigationDestinations[1],
                    isSelected: currentIndex == NavigationItem.explore,
                  ),
                  // Empty space for center button
                  const SizedBox(width: 60),
                  _buildModernFancyNavItem(
                    context: context,
                    ref: ref,
                    destination: navigationDestinations[3],
                    isSelected: currentIndex == NavigationItem.messages,
                  ),
                  _buildModernFancyNavItem(
                    context: context,
                    ref: ref,
                    destination: navigationDestinations[4],
                    isSelected: currentIndex == NavigationItem.profile,
                  ),
                ],
              ),
            ),

            // Center floating button
            Positioned(
              top: -16,
              child: Container(
                height: 56,
                width: 56,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withAlpha(51),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      ref
                          .read(navigationProvider.notifier)
                          .setNavigationItem(NavigationItem.bookings);
                    },
                    customBorder: const CircleBorder(),
                    child: Icon(
                      navigationDestinations[2].selectedIcon,
                      color: Colors.white,
                      size: iconSize,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernFancyNavItem({
    required BuildContext context,
    required WidgetRef ref,
    required CustomNavigationDestination destination,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        ref
            .read(navigationProvider.notifier)
            .setNavigationItem(destination.item);
      },
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon with animated background
            AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor.withAlpha(26)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    isSelected ? destination.selectedIcon : destination.icon,
                    key: ValueKey(isSelected),
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
                    size: iconSize,
                  ),
                ),
              ),
            ),
            if (showLabels) ...[
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.textSecondaryColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  fontSize: isSelected ? selectedFontSize : unselectedFontSize,
                ),
                child: Text(
                  destination.label,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _ModernNavigationItem extends StatelessWidget {
  final CustomNavigationDestination destination;
  final bool isSelected;
  final bool showLabel;
  final double iconSize;
  final double selectedFontSize;
  final double unselectedFontSize;
  final VoidCallback onTap;

  const _ModernNavigationItem({
    required this.destination,
    required this.isSelected,
    required this.onTap,
    this.showLabel = true,
    this.iconSize = 24,
    this.selectedFontSize = 12,
    this.unselectedFontSize = 10,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon with animated background
            AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.primaryColor.withAlpha(26)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    isSelected ? destination.selectedIcon : destination.icon,
                    key: ValueKey(isSelected),
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
                    size: iconSize,
                  ),
                ),
              ),
            ),
            if (showLabel) ...[
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.textSecondaryColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  fontSize: isSelected ? selectedFontSize : unselectedFontSize,
                ),
                child: Text(
                  destination.label,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// The style of the bottom navigation bar
enum BottomNavStyle {
  /// Standard navigation bar with equal-sized items
  standard,

  /// Fancy navigation bar with a prominent center button
  fancy,
}
