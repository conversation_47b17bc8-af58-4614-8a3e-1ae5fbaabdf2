// Dart imports

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'package:local_auth/local_auth.dart';

// Project imports
import 'package:culture_connect/models/user_model.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  verificationPending,
}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final LocalAuthentication _localAuth = LocalAuthentication();

  // Stream to listen to authentication state changes
  Stream<AuthStatus> get authStateChanges {
    return _auth.authStateChanges().map((user) {
      if (user == null) {
        return AuthStatus.unauthenticated;
      } else if (!user.emailVerified) {
        return AuthStatus.verificationPending;
      } else {
        return AuthStatus.authenticated;
      }
    }).handleError((error) {
      debugPrint('Auth state change error: $error');
      // Return unauthenticated as fallback
      return AuthStatus.unauthenticated;
    });
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current authentication status synchronously
  AuthStatus get currentAuthStatus {
    final user = _auth.currentUser;
    if (user == null) {
      return AuthStatus.unauthenticated;
    } else if (!user.emailVerified) {
      return AuthStatus.verificationPending;
    } else {
      return AuthStatus.authenticated;
    }
  }

  // Get current user as UserModel with retry logic
  Future<UserModel?> get currentUserModel async {
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('AuthService: No current user found');
      return null;
    }

    debugPrint('AuthService: Getting user model for UID: ${user.uid}');

    return await _retryFirestoreOperation(() async {
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        final data = doc.data()!;
        debugPrint('AuthService: Firestore user data: $data');

        final userModel = UserModel.fromJson({
          'id': user.uid,
          'email': user.email ?? data['email'] ?? '',
          'emailVerified': user.emailVerified,
          ...data,
        });

        debugPrint(
            'AuthService: Created UserModel with name: ${userModel.fullName}');
        return userModel;
      } else {
        debugPrint(
            'AuthService: No Firestore document found for user ${user.uid}');

        // If no Firestore document exists, create a basic UserModel from Firebase Auth data
        final displayName = user.displayName ?? '';
        final nameParts = displayName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : '';
        final lastName =
            nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

        final basicUserModel = UserModel(
          id: user.uid,
          firstName: firstName,
          lastName: lastName,
          email: user.email ?? '',
          phoneNumber: user.phoneNumber ?? '',
          userType: 'tourist',
          isVerified: user.emailVerified,
          verificationLevel: 1,
          status: 'active',
          createdAt: DateTime.now().toIso8601String(),
          updatedAt: DateTime.now().toIso8601String(),
          lastLogin: DateTime.now().toIso8601String(),
          emailVerified: user.emailVerified,
        );

        debugPrint(
            'AuthService: Created basic UserModel with name: ${basicUserModel.fullName}');
        return basicUserModel;
      }
    }, operationName: 'getCurrentUserModel');
  }

  /// Retry Firestore operations with exponential backoff
  Future<T?> _retryFirestoreOperation<T>(
    Future<T?> Function() operation, {
    required String operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;
    Duration delay = initialDelay;

    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        retryCount++;

        // Check if this is a retryable error
        if (!_isRetryableError(e) || retryCount >= maxRetries) {
          debugPrint('$operationName failed after $retryCount attempts: $e');
          return null;
        }

        debugPrint(
            '$operationName attempt $retryCount failed: $e. Retrying in ${delay.inSeconds}s...');

        // Wait before retrying with exponential backoff
        await Future.delayed(delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * 1.5).round());
      }
    }

    return null;
  }

  /// Check if an error is retryable
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Retryable Firebase errors
    return errorString.contains('unavailable') ||
        errorString.contains('deadline-exceeded') ||
        errorString.contains('internal') ||
        errorString.contains('timeout') ||
        errorString.contains('network') ||
        errorString.contains('connection');
  }

  // Register with email and password
  Future<UserCredential> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String phoneNumber,
    required String dateOfBirth,
  }) async {
    try {
      // Create user with email and password
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Store additional user data in Firestore
      if (userCredential.user != null) {
        await _storeUserData(
          userCredential.user!.uid,
          firstName: firstName,
          lastName: lastName,
          email: email,
          phoneNumber: phoneNumber,
          dateOfBirth: dateOfBirth,
          userType: 'tourist',
        );

        // Send email verification with error handling
        try {
          await userCredential.user!.sendEmailVerification();
          debugPrint(
              'Email verification sent successfully to ${userCredential.user!.email}');
        } catch (emailError) {
          debugPrint('Error sending email verification: $emailError');
          // Don't throw here - registration should still succeed even if email fails
        }
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during registration: $e');
      rethrow;
    }
  }

  // Login with email and password
  Future<UserCredential> loginWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update last login timestamp with retry logic
      if (userCredential.user != null) {
        await _retryFirestoreOperation(() async {
          await _firestore
              .collection('users')
              .doc(userCredential.user!.uid)
              .update({
            'lastLogin': DateTime.now().toIso8601String(),
          });
          return true;
        }, operationName: 'updateLastLogin');
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during login: $e');
      rethrow;
    }
  }

  // Sign in with Google (Web-Compatible Implementation)
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Create a GoogleAuthProvider instance
      final GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // Add scopes for additional user information
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Sign in with popup for web compatibility
      final UserCredential userCredential =
          await _auth.signInWithPopup(googleProvider);

      // Verify we have a valid user
      if (userCredential.user == null) {
        debugPrint('Google sign-in failed: No user returned');
        return null;
      }

      // Check if this is a new user
      if (userCredential.additionalUserInfo?.isNewUser ?? false) {
        // Store basic user data in Firestore
        final displayName = userCredential.user?.displayName ?? '';
        final nameParts = displayName.split(' ');
        final firstName = nameParts.isNotEmpty ? nameParts.first : '';
        final lastName = nameParts.length > 1 ? nameParts.last : '';

        await _storeUserDataWithRetry(
          userCredential.user!.uid,
          firstName: firstName,
          lastName: lastName,
          email: userCredential.user?.email ?? '',
          phoneNumber: userCredential.user?.phoneNumber ?? '',
          userType: 'tourist',
        );
      } else {
        // Update last login timestamp with retry logic
        await _retryFirestoreOperation(() async {
          await _firestore
              .collection('users')
              .doc(userCredential.user!.uid)
              .update({
            'lastLogin': DateTime.now().toIso8601String(),
          });
          return true;
        }, operationName: 'updateLastLoginGoogle');
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error during Google sign in: $e');
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Exception: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Unexpected error sending password reset: $e');
      rethrow;
    }
  }

  // Verify email using token-based approach to avoid PigeonUserInfo issues
  Future<bool> verifyEmail() async {
    try {
      User? user = _auth.currentUser;
      if (user == null) return false;

      // Primary method: Use ID token to check verification status
      try {
        final idTokenResult = await user.getIdTokenResult(true);
        final emailVerified = idTokenResult.claims?['email_verified'] ?? false;
        debugPrint('Token-based email verification check: $emailVerified');

        // CRITICAL FIX: Update Firestore when verification is confirmed
        if (emailVerified == true) {
          try {
            await _firestore.collection('users').doc(user.uid).update({
              'emailVerified': true,
              'isVerified': true, // Mark user as verified
              'updatedAt': DateTime.now().toIso8601String(),
            });
            debugPrint('Firestore updated with email verification status');
          } catch (firestoreError) {
            debugPrint(
                'Error updating Firestore verification status: $firestoreError');
            // Don't fail the verification if Firestore update fails
          }
        }

        return emailVerified == true;
      } catch (tokenError) {
        debugPrint('Token-based verification failed: $tokenError');

        // Fallback: Try standard reload method
        try {
          await user.reload();
          user = _auth.currentUser;
          final isVerified = user?.emailVerified ?? false;

          // CRITICAL FIX: Update Firestore for fallback method too
          if (isVerified && user != null) {
            try {
              await _firestore.collection('users').doc(user.uid).update({
                'emailVerified': true,
                'isVerified': true,
                'updatedAt': DateTime.now().toIso8601String(),
              });
              debugPrint('Firestore updated via fallback method');
            } catch (firestoreError) {
              debugPrint(
                  'Error updating Firestore via fallback: $firestoreError');
            }
          }

          return isVerified;
        } catch (reloadError) {
          debugPrint('Standard reload verification failed: $reloadError');
          return false;
        }
      }
    } catch (e) {
      debugPrint('Error verifying email: $e');
      return false;
    }
  }

  // Resend verification email with improved error handling
  Future<void> resendVerificationEmail() async {
    try {
      User? user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      if (user.emailVerified) {
        throw Exception('Email is already verified');
      }

      await user.sendEmailVerification();
      debugPrint('Verification email resent successfully to ${user.email}');
    } on FirebaseAuthException catch (e) {
      debugPrint(
          'Firebase Auth Exception resending email: ${e.code} - ${e.message}');

      // Handle specific Firebase Auth errors
      switch (e.code) {
        case 'too-many-requests':
          throw Exception(
              'Too many requests. Please wait before requesting another email.');
        case 'user-disabled':
          throw Exception('This user account has been disabled.');
        case 'user-not-found':
          throw Exception('User account not found.');
        default:
          throw Exception('Failed to send verification email: ${e.message}');
      }
    } catch (e) {
      debugPrint('Error resending verification email: $e');
      rethrow;
    }
  }

  // Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      debugPrint('Error checking biometric availability: $e');
      return false;
    }
  }

  // Authenticate with biometrics
  Future<bool> authenticateWithBiometrics() async {
    try {
      return await _localAuth.authenticate(
        localizedReason: 'Authenticate to access your account',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } catch (e) {
      debugPrint('Error authenticating with biometrics: $e');
      return false;
    }
  }

  // Store user data in Firestore with retry logic
  Future<void> _storeUserDataWithRetry(
    String userId, {
    required String firstName,
    required String lastName,
    required String email,
    required String phoneNumber,
    String? dateOfBirth,
    required String userType,
  }) async {
    await _retryFirestoreOperation(() async {
      final userData = {
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'phoneNumber': phoneNumber,
        'dateOfBirth': dateOfBirth,
        'userType': userType,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
        'isVerified': false,
        'emailVerified':
            false, // CRITICAL FIX: Add emailVerified field for UserModel consistency
        'verificationLevel': 1,
        'status': 'active',
        'twoFactorEnabled': false, // SECURITY: Initialize 2FA setting
      };

      await _firestore.collection('users').doc(userId).set(userData);
      return true;
    }, operationName: 'storeUserData');
  }

  // Store user data in Firestore (legacy method for backward compatibility)
  Future<void> _storeUserData(
    String userId, {
    required String firstName,
    required String lastName,
    required String email,
    required String phoneNumber,
    String? dateOfBirth,
    required String userType,
  }) async {
    try {
      final userData = {
        'firstName': firstName,
        'lastName': lastName,
        'email': email,
        'phoneNumber': phoneNumber,
        'dateOfBirth': dateOfBirth,
        'userType': userType,
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'lastLogin': DateTime.now().toIso8601String(),
        'isVerified': false,
        'emailVerified':
            false, // CRITICAL FIX: Add emailVerified field for UserModel consistency
        'verificationLevel': 1,
        'status': 'active',
        'twoFactorEnabled': false, // SECURITY: Initialize 2FA setting
      };

      await _firestore.collection('users').doc(userId).set(userData);
    } catch (e) {
      debugPrint('Error storing user data: $e');
      rethrow;
    }
  }
}
