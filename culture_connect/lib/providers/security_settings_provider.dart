import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/security_settings.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';

/// Keys for SharedPreferences storage
class _SecuritySettingsKeys {
  static const String autoLockEnabled = 'security_auto_lock_enabled';
  static const String autoLockTimeoutMinutes =
      'security_auto_lock_timeout_minutes';
  static const String biometricEnabled = 'security_biometric_enabled';
  static const String requirePasswordFallback =
      'security_require_password_fallback';
  static const String lockOnAppBackground = 'security_lock_on_app_background';
  static const String showLockScreenPreview =
      'security_show_lock_screen_preview';
}

/// Security settings notifier for managing auto-lock and authentication preferences
class SecuritySettingsNotifier extends StateNotifier<SecuritySettings> {
  final SharedPreferences _prefs;

  SecuritySettingsNotifier(this._prefs) : super(const SecuritySettings()) {
    _loadSettings();
  }

  /// Load settings from SharedPreferences
  void _loadSettings() {
    state = SecuritySettings(
      // TEMPORARY: Auto-lock disabled for debugging authentication issues
      // TODO: Re-enable after fixing authentication logic
      autoLockEnabled: _prefs.getBool(_SecuritySettingsKeys.autoLockEnabled) ??
          false, // Changed from true to false
      autoLockTimeoutMinutes:
          _prefs.getInt(_SecuritySettingsKeys.autoLockTimeoutMinutes) ?? 5,
      biometricEnabled:
          _prefs.getBool(_SecuritySettingsKeys.biometricEnabled) ?? true,
      requirePasswordFallback:
          _prefs.getBool(_SecuritySettingsKeys.requirePasswordFallback) ?? true,
      lockOnAppBackground:
          _prefs.getBool(_SecuritySettingsKeys.lockOnAppBackground) ?? true,
      showLockScreenPreview:
          _prefs.getBool(_SecuritySettingsKeys.showLockScreenPreview) ?? false,
    );
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    await Future.wait([
      _prefs.setBool(
          _SecuritySettingsKeys.autoLockEnabled, state.autoLockEnabled),
      _prefs.setInt(_SecuritySettingsKeys.autoLockTimeoutMinutes,
          state.autoLockTimeoutMinutes),
      _prefs.setBool(
          _SecuritySettingsKeys.biometricEnabled, state.biometricEnabled),
      _prefs.setBool(_SecuritySettingsKeys.requirePasswordFallback,
          state.requirePasswordFallback),
      _prefs.setBool(
          _SecuritySettingsKeys.lockOnAppBackground, state.lockOnAppBackground),
      _prefs.setBool(_SecuritySettingsKeys.showLockScreenPreview,
          state.showLockScreenPreview),
    ]);
  }

  /// Enable or disable auto-lock
  Future<void> setAutoLockEnabled(bool enabled) async {
    state = state.copyWith(autoLockEnabled: enabled);
    await _saveSettings();
  }

  /// Set auto-lock timeout in minutes
  Future<void> setAutoLockTimeout(int minutes) async {
    state = state.copyWith(autoLockTimeoutMinutes: minutes);
    await _saveSettings();
  }

  /// Enable or disable biometric authentication
  Future<void> setBiometricEnabled(bool enabled) async {
    state = state.copyWith(biometricEnabled: enabled);
    await _saveSettings();
  }

  /// Set password fallback requirement
  Future<void> setRequirePasswordFallback(bool required) async {
    state = state.copyWith(requirePasswordFallback: required);
    await _saveSettings();
  }

  /// Enable or disable lock on app background
  Future<void> setLockOnAppBackground(bool enabled) async {
    state = state.copyWith(lockOnAppBackground: enabled);
    await _saveSettings();
  }

  /// Enable or disable lock screen preview
  Future<void> setShowLockScreenPreview(bool enabled) async {
    state = state.copyWith(showLockScreenPreview: enabled);
    await _saveSettings();
  }

  /// Update multiple settings at once
  Future<void> updateSettings(SecuritySettings newSettings) async {
    state = newSettings;
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    state = const SecuritySettings();
    await _saveSettings();
  }
}

/// Provider for security settings
final securitySettingsProvider =
    StateNotifierProvider<SecuritySettingsNotifier, SecuritySettings>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  return SecuritySettingsNotifier(prefs);
});

/// Convenience providers for specific settings
final autoLockEnabledProvider = Provider<bool>((ref) {
  return ref.watch(securitySettingsProvider).autoLockEnabled;
});

final autoLockTimeoutProvider = Provider<Duration>((ref) {
  return ref.watch(securitySettingsProvider).timeoutDuration;
});

final biometricEnabledProvider = Provider<bool>((ref) {
  return ref.watch(securitySettingsProvider).biometricEnabled;
});

final lockOnAppBackgroundProvider = Provider<bool>((ref) {
  return ref.watch(securitySettingsProvider).lockOnAppBackground;
});
