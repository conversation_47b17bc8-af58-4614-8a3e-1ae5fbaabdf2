import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum NavigationItem {
  home,
  explore,
  bookings,
  messages,
  profile,
}

final navigationProvider =
    StateNotifierProvider<NavigationNotifier, NavigationItem>((ref) {
  return NavigationNotifier();
});

class NavigationNotifier extends StateNotifier<NavigationItem> {
  NavigationNotifier() : super(NavigationItem.home);

  void setNavigationItem(NavigationItem item) {
    state = item;
  }
}

class CustomNavigationDestination {
  final NavigationItem item;
  final String label;
  final IconData icon;
  final IconData selectedIcon;

  const CustomNavigationDestination({
    required this.item,
    required this.label,
    required this.icon,
    required this.selectedIcon,
  });
}

final navigationDestinations = [
  const CustomNavigationDestination(
    item: NavigationItem.home,
    label: 'Home',
    icon: Icons.home_outlined,
    selectedIcon: Icons.home_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.explore,
    label: 'Explore',
    icon: Icons.explore_outlined,
    selectedIcon: Icons.explore_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.bookings,
    label: 'Bookings',
    icon: Icons.bookmark_outline_rounded,
    selectedIcon: Icons.bookmark_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.messages,
    label: 'Messages',
    icon: Icons.chat_bubble_outline_rounded,
    selectedIcon: Icons.chat_bubble_rounded,
  ),
  const CustomNavigationDestination(
    item: NavigationItem.profile,
    label: 'Profile',
    icon: Icons.person_outline_rounded,
    selectedIcon: Icons.person_rounded,
  ),
];
