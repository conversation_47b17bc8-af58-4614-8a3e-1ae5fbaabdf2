import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/auth_provider.dart';

import 'package:culture_connect/widgets/experience_card.dart';
import 'package:shimmer/shimmer.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  final _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isLoading = true;
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
    _simulateLoading();
  }

  Future<void> _simulateLoading() async {
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: _isLoading ? _buildLoadingState() : _buildCurrentScreen(),
    );
  }

  Widget _buildLoadingState() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                height: 180,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const SizedBox(height: 32),
              Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentScreen() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildHomeScreen(),
    );
  }

  Widget _buildHomeScreen() {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _isLoading = true;
        });
        await _simulateLoading();
      },
      color: AppTheme.primaryColor,
      backgroundColor: Colors.white,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),

              // Welcome Section with Animation
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Enhanced Search Bar
              _buildEnhancedSearchBar(),

              const SizedBox(height: 24),

              // Hero Section with Parallax Effect
              _buildHeroSection(),

              const SizedBox(height: 32),

              // Quick Actions with Modern Design
              _buildQuickActionsSection(),

              const SizedBox(height: 32),

              // Explore by Category Section
              _buildExploreByCategorySection(),

              const SizedBox(height: 32),

              // Featured Experiences with Enhanced Cards
              _buildFeaturedExperiencesSection(),

              const SizedBox(height: 32),

              // Top Guides with AirBnB-style Cards
              _buildTopGuidesSection(),

              const SizedBox(height: 32),

              // Nearby Experiences with Map Preview
              _buildNearbyExperiencesSection(),

              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Row(
      children: [
        // Hamburger Menu
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(8),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: const Icon(
            Icons.menu_rounded,
            color: AppTheme.textPrimaryColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        // Name and Explorer Badge - Fixed overflow issue
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'Hi, ',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  // Flexible wrapper to prevent overflow
                  Flexible(
                    child: Consumer(
                      builder: (context, ref, child) {
                        final userAsync = ref.watch(currentUserModelProvider);
                        return userAsync.when(
                          data: (user) {
                            // Debug logging to understand what data we're getting
                            debugPrint(
                                'Home screen user data: ${user?.toJson()}');

                            String displayName = 'Explorer';
                            if (user != null) {
                              if (user.firstName.isNotEmpty) {
                                displayName = user.firstName;
                              } else if (user.fullName.isNotEmpty) {
                                displayName = user.fullName.split(' ').first;
                              }
                            }

                            return Text(
                              displayName,
                              style: const TextStyle(
                                color: AppTheme.textPrimaryColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            );
                          },
                          loading: () => Container(
                            height: 18,
                            width: 60,
                            decoration: BoxDecoration(
                              color: AppTheme.surfaceColor.withAlpha(128),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          error: (error, stackTrace) {
                            // Debug logging for errors
                            debugPrint('Error loading user data: $error');
                            debugPrint('Stack trace: $stackTrace');

                            return const Text(
                              'Explorer',
                              style: TextStyle(
                                color: AppTheme.textPrimaryColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            );
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Explorer Badge - Constrained to prevent overflow
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Explorer',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              const Text(
                'Let\'s explore the world!',
                style: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // Notification Icon
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(8),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              const Center(
                child: Icon(
                  Icons.notifications_outlined,
                  color: AppTheme.textPrimaryColor,
                  size: 20,
                ),
              ),
              // Notification badge
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedSearchBar() {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(26),
        border: Border.all(
          color: const Color(0xFFE9ECEF),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 12),
            child: Icon(
              Icons.search_rounded,
              color: Color(0xFF6C757D),
              size: 20,
            ),
          ),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: const TextStyle(
                fontSize: 15,
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w400,
              ),
              decoration: const InputDecoration(
                hintText: 'Search destinations, experiences...',
                hintStyle: TextStyle(
                  color: Color(0xFF6C757D),
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onTap: () {
                // TODO: Navigate to search screen
              },
            ),
          ),
          // Filter Icon
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(18),
              ),
              child: const Icon(
                Icons.tune_rounded,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Background image
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withAlpha(200),
                    AppTheme.accentColor.withAlpha(150),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),

            // Overlay gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withAlpha(100),
                    Colors.transparent,
                    Colors.black.withAlpha(150),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Content
            Positioned(
              left: 20,
              bottom: 20,
              right: 20,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Discover Amazing Places',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Explore cultural experiences around the world',
                    style: TextStyle(
                      color: Colors.white.withAlpha(230),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            children: [
              _buildModernQuickLink(
                icon: Icons.flight_takeoff_rounded,
                label: 'Flights',
                backgroundColor: const Color(0xFF4A90E2),
                onTap: () =>
                    Navigator.pushNamed(context, '/travel/flights/search'),
              ),
              _buildModernQuickLink(
                icon: Icons.hotel_rounded,
                label: 'Hotels',
                backgroundColor: const Color(0xFF50C878),
                onTap: () => Navigator.pushNamed(context, '/travel/hotels'),
              ),
              _buildModernQuickLink(
                icon: Icons.restaurant_rounded,
                label: 'Restaurants',
                backgroundColor: const Color(0xFFFF6B6B),
                onTap: () =>
                    Navigator.pushNamed(context, '/travel/restaurants'),
              ),
              _buildModernQuickLink(
                icon: Icons.local_taxi_rounded,
                label: 'Transport',
                backgroundColor: const Color(0xFFFFD93D),
                onTap: () => Navigator.pushNamed(context, '/travel/transport'),
              ),
              _buildModernQuickLink(
                icon: Icons.explore_rounded,
                label: 'Explore',
                backgroundColor: const Color(0xFF9B59B6),
                onTap: () => Navigator.pushNamed(context, '/explore'),
              ),
              _buildModernQuickLink(
                icon: Icons.translate_rounded,
                label: 'Translate',
                backgroundColor: const Color(0xFF1ABC9C),
                onTap: () => Navigator.pushNamed(context, '/voice-translation'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernQuickLink({
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 90,
      margin: const EdgeInsets.only(right: 16),
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: backgroundColor.withAlpha(51),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(8),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExploreByCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Categories',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushNamed(context, '/explore'),
              child: const Text(
                'See all',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.8,
          children: [
            _buildModernCategoryCard(
              title: 'Cultural Tours',
              icon: Icons.account_balance_rounded,
              backgroundColor: const Color(0xFF6B73FF),
              isSelected: true,
              onTap: () =>
                  Navigator.pushNamed(context, '/explore?category=cultural'),
            ),
            _buildModernCategoryCard(
              title: 'Food & Dining',
              icon: Icons.restaurant_rounded,
              backgroundColor: const Color(0xFFFF6B6B),
              isSelected: false,
              onTap: () =>
                  Navigator.pushNamed(context, '/explore?category=food'),
            ),
            _buildModernCategoryCard(
              title: 'Art & Crafts',
              icon: Icons.palette_rounded,
              backgroundColor: const Color(0xFF4ECDC4),
              isSelected: false,
              onTap: () =>
                  Navigator.pushNamed(context, '/explore?category=art'),
            ),
            _buildModernCategoryCard(
              title: 'Music & Dance',
              icon: Icons.music_note_rounded,
              backgroundColor: const Color(0xFFFFE66D),
              isSelected: false,
              onTap: () =>
                  Navigator.pushNamed(context, '/explore?category=music'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernCategoryCard({
    required String title,
    required IconData icon,
    required Color backgroundColor,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? backgroundColor : const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? backgroundColor : const Color(0xFFE9ECEF),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: backgroundColor.withAlpha(51),
                    blurRadius: 8,
                    offset: const Offset(0, 3),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withAlpha(5),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.white.withAlpha(51)
                      : backgroundColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Icon(
                    icon,
                    color: isSelected ? Colors.white : backgroundColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color:
                        isSelected ? Colors.white : AppTheme.textPrimaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Featured Experiences',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/explore');
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: _buildOptimizedFeaturedExperiencesList(),
        ),
      ],
    );
  }

  // Optimized: Featured experiences with lazy loading and const data
  Widget _buildOptimizedFeaturedExperiencesList() {
    // Pre-defined const data for better performance
    const featuredExperiences = [
      {
        'title': 'Yoruba Cooking Class',
        'location': 'Lagos, Nigeria',
        'imageUrl': 'https://via.placeholder.com/400x300?text=Cooking+Class',
        'rating': 4.8,
        'price': '\$45',
        'isFeatured': true,
        'category': 'Culinary',
        'reviewCount': 128,
      },
      {
        'title': 'Maasai Dance Workshop',
        'location': 'Nairobi, Kenya',
        'imageUrl': 'https://via.placeholder.com/400x300?text=Dance+Workshop',
        'rating': 4.6,
        'price': '\$35',
        'isFeatured': false,
        'category': 'Dance',
        'reviewCount': 89,
      },
      {
        'title': 'Cape Town Cultural Tour',
        'location': 'Cape, South Africa',
        'imageUrl': 'https://via.placeholder.com/400x300?text=Cultural+Tour',
        'rating': 4.9,
        'price': '\$60',
        'isFeatured': true,
        'category': 'Tour',
        'reviewCount': 256,
      },
    ];

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      itemCount: featuredExperiences.length,
      // Optimized: Add caching for better performance
      cacheExtent: 1000,
      itemBuilder: (context, index) {
        final experience = featuredExperiences[index];
        return Container(
          width: 240,
          margin: EdgeInsets.only(
            right: index < featuredExperiences.length - 1 ? 16 : 0,
          ),
          child: ExperienceCard(
            title: experience['title'] as String,
            location: experience['location'] as String,
            imageUrl: experience['imageUrl'] as String,
            rating: experience['rating'] as double,
            price: experience['price'] as String,
            isFeatured: experience['isFeatured'] as bool,
            category: experience['category'] as String,
            reviewCount: experience['reviewCount'] as int,
            onTap: () {
              // TODO: Navigate to experience details
            },
          ),
        );
      },
    );
  }

  Widget _buildTopGuidesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Top Local Guides',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/guides');
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: 4,
            itemBuilder: (context, index) {
              final guides = [
                {
                  'name': 'Adebayo O.',
                  'location': 'Lagos',
                  'imageUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
                  'rating': 4.9,
                  'specialties': ['Culinary', 'History'],
                },
                {
                  'name': 'Wanjiku M.',
                  'location': 'Nairobi',
                  'imageUrl':
                      'https://randomuser.me/api/portraits/women/44.jpg',
                  'rating': 4.8,
                  'specialties': ['Wildlife', 'Culture'],
                },
                {
                  'name': 'Thabo N.',
                  'location': 'Cape Town',
                  'imageUrl': 'https://randomuser.me/api/portraits/men/22.jpg',
                  'rating': 4.7,
                  'specialties': ['Wine', 'Tours'],
                },
                {
                  'name': 'Amara C.',
                  'location': 'Accra',
                  'imageUrl':
                      'https://randomuser.me/api/portraits/women/67.jpg',
                  'rating': 4.9,
                  'specialties': ['Art', 'Music'],
                },
              ];

              final guide = guides[index];
              return Container(
                margin: EdgeInsets.only(
                  right: index < guides.length - 1 ? 20 : 0,
                ),
                child: _buildEnhancedGuideItem(
                  name: guide['name'] as String,
                  location: guide['location'] as String,
                  imageUrl: guide['imageUrl'] as String,
                  rating: guide['rating'] as double,
                  specialties: guide['specialties'] as List<String>,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedGuideItem({
    required String name,
    required String location,
    required String imageUrl,
    required double rating,
    required List<String> specialties,
  }) {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Profile image
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: NetworkImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Info section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 12,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          location,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 12,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        rating.toString(),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Nearby Experiences',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to explore screen with location filter
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Traditional Drum Circle Workshop',
          location: 'Ikeja, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Drum+Workshop',
          rating: 4.7,
          price: '\$30',
          duration: '2 hours',
          isHorizontal: true,
          category: 'Music',
          reviewCount: 45,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Authentic Nigerian Cuisine Tour',
          location: 'Victoria Island, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Cuisine+Tour',
          rating: 4.5,
          price: '\$40',
          duration: '3 hours',
          isHorizontal: true,
          category: 'Food',
          reviewCount: 78,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
      ],
    );
  }
}
