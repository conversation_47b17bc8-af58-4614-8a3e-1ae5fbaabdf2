import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/common/image_gallery.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/booking/instant_booking_button.dart';
import 'package:culture_connect/widgets/travel/hotel_review_list.dart';
import 'package:culture_connect/widgets/travel/hotel_map_view.dart';

/// Redesigned hotel details screen with modern, visually stunning interface
class HotelDetailsScreenRedesigned extends ConsumerStatefulWidget {
  final Hotel hotel;

  const HotelDetailsScreenRedesigned({
    super.key,
    required this.hotel,
  });

  @override
  ConsumerState<HotelDetailsScreenRedesigned> createState() =>
      _HotelDetailsScreenRedesignedState();
}

class _HotelDetailsScreenRedesignedState
    extends ConsumerState<HotelDetailsScreenRedesigned>
    with TickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _headerAnimationController;
  late AnimationController _bookingAnimationController;
  late Animation<double> _headerOpacity;
  late Animation<double> _bookingScale;

  DateTime _checkInDate = DateTime.now().add(const Duration(days: 1));
  DateTime _checkOutDate = DateTime.now().add(const Duration(days: 2));
  int _guests = 2;
  HotelRoom? _selectedRoom;
  double _totalPrice = 0.0;
  int _nights = 1;
  bool _showFullDescription = false;
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _calculateTotalPrice();
  }

  void _initializeControllers() {
    _scrollController = ScrollController();
    _headerAnimationController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _bookingAnimationController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );

    _headerOpacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: AppTheme.curveEmphasized,
      ),
    );

    _bookingScale = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _bookingAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _scrollController.addListener(_handleScroll);
    _headerAnimationController.forward();
    _bookingAnimationController.forward();
  }

  void _handleScroll() {
    final offset = _scrollController.offset;
    final opacity = (offset / 200).clamp(0.0, 1.0);
    _headerAnimationController.animateTo(opacity);
  }

  void _calculateTotalPrice() {
    _nights = _checkOutDate.difference(_checkInDate).inDays;
    if (_nights < 1) _nights = 1;

    if (_selectedRoom != null) {
      _totalPrice = _selectedRoom!.pricePerNight * _nights;
    } else {
      _totalPrice = widget.hotel.price * _nights;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _headerAnimationController.dispose();
    _bookingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Stack(
        children: [
          // Main Content
          CustomScrollView(
            controller: _scrollController,
            slivers: [
              // Hero Image Section
              _buildHeroImageSection(),
              // Content Sections
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    _buildPropertyHeader(),
                    _buildQuickInfo(),
                    _buildImageGallery(),
                    _buildDescription(),
                    _buildAmenities(),
                    _buildRoomsSection(),
                    _buildLocationSection(),
                    _buildReviewsSection(),
                    const SizedBox(height: 120), // Space for booking bar
                  ],
                ),
              ),
            ],
          ),
          // Animated Header
          _buildAnimatedHeader(),
          // Floating Booking Bar
          _buildFloatingBookingBar(),
        ],
      ),
    );
  }

  Widget _buildHeroImageSection() {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: false,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(AppTheme.spacingSmall),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(128),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(AppTheme.spacingSmall),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(128),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          ),
          child: IconButton(
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? AppTheme.errorColor : Colors.white,
            ),
            onPressed: () {
              setState(() {
                _isFavorite = !_isFavorite;
              });
            },
          ),
        ),
        Container(
          margin: const EdgeInsets.all(AppTheme.spacingSmall),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(128),
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          ),
          child: IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () {
              // Handle share
            },
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            Hero(
              tag: 'hotel_image_${widget.hotel.id}',
              child: Image.network(
                widget.hotel.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.surfaceVariant,
                          AppTheme.surfaceColor,
                        ],
                      ),
                    ),
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppTheme.textSecondaryColor,
                      size: 64,
                    ),
                  );
                },
              ),
            ),
            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(77),
                  ],
                ),
              ),
            ),
            // Price badge
            Positioned(
              bottom: AppTheme.spacingLarge,
              right: AppTheme.spacingLarge,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingSmall,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                  boxShadow: AppTheme.shadowMedium,
                ),
                child: Text(
                  '${widget.hotel.currency}${widget.hotel.price.toStringAsFixed(0)}/night',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedHeader() {
    return AnimatedBuilder(
      animation: _headerOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _headerOpacity.value,
          child: Container(
            height: MediaQuery.of(context).padding.top + 56,
            decoration: BoxDecoration(
              gradient: AppTheme.cardGradient,
              boxShadow: AppTheme.shadowSmall,
            ),
            child: SafeArea(
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () => Navigator.pop(context),
                  ),
                  Expanded(
                    child: Text(
                      widget.hotel.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      _isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: _isFavorite
                          ? AppTheme.errorColor
                          : AppTheme.textSecondaryColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _isFavorite = !_isFavorite;
                      });
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.share,
                        color: AppTheme.textSecondaryColor),
                    onPressed: () {
                      // Handle share
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPropertyHeader() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hotel name and star rating
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.hotel.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingXS),
                    Row(
                      children: [
                        ...List.generate(
                          widget.hotel.starRating.value,
                          (index) => const Icon(
                            Icons.star,
                            color: Color(0xFFFFB800),
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Text(
                          widget.hotel.starRating.displayName,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Rating badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingSmall,
                  vertical: AppTheme.spacingXS,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.accentGradient,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: AppTheme.spacingXS),
                    Text(
                      widget.hotel.rating.toStringAsFixed(1),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          // Location
          Row(
            children: [
              const Icon(
                Icons.location_on_outlined,
                color: AppTheme.textSecondaryColor,
                size: 18,
              ),
              const SizedBox(width: AppTheme.spacingXS),
              Expanded(
                child: Text(
                  widget.hotel.location,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          // Reviews count
          Row(
            children: [
              const Icon(
                Icons.reviews_outlined,
                color: AppTheme.textSecondaryColor,
                size: 18,
              ),
              const SizedBox(width: AppTheme.spacingXS),
              Text(
                '${widget.hotel.reviewCount} reviews',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // Scroll to reviews section
                },
                child: Text(
                  'Read reviews',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInfo() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spacingMedium),
      child: Row(
        children: [
          Expanded(
            child: _buildInfoCard(
              icon: Icons.access_time_outlined,
              title: 'Check-in',
              subtitle: widget.hotel.formattedCheckInTime,
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: _buildInfoCard(
              icon: Icons.access_time_filled_outlined,
              title: 'Check-out',
              subtitle: widget.hotel.formattedCheckOutTime,
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: _buildInfoCard(
              icon: Icons.people_outline,
              title: 'Guests',
              subtitle:
                  'Up to ${widget.hotel.rooms.isNotEmpty ? widget.hotel.rooms.first.maxGuests : 2}',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.surfaceColor,
            AppTheme.surfaceVariant,
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 24,
          ),
          const SizedBox(height: AppTheme.spacingXS),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXS),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildImageGallery() {
    if (widget.hotel.additionalImages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Photo Gallery',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          ImageGallery(
            images: [widget.hotel.imageUrl, ...widget.hotel.additionalImages],
            height: 200,
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'About this property',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            _showFullDescription
                ? widget.hotel.description
                : widget.hotel.description.length > 200
                    ? '${widget.hotel.description.substring(0, 200)}...'
                    : widget.hotel.description,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (widget.hotel.description.length > 200) ...[
            const SizedBox(height: AppTheme.spacingMedium),
            TextButton(
              onPressed: () {
                setState(() {
                  _showFullDescription = !_showFullDescription;
                });
              },
              child: Text(
                _showFullDescription ? 'Show less' : 'Show more',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAmenities() {
    final amenities = [
      if (widget.hotel.hasFreeWifi) {'icon': Icons.wifi, 'label': 'Free WiFi'},
      if (widget.hotel.hasFreeParking)
        {'icon': Icons.local_parking, 'label': 'Free Parking'},
      if (widget.hotel.hasPool) {'icon': Icons.pool, 'label': 'Swimming Pool'},
      if (widget.hotel.hasGym)
        {'icon': Icons.fitness_center, 'label': 'Fitness Center'},
      if (widget.hotel.hasSpa) {'icon': Icons.spa, 'label': 'Spa'},
      if (widget.hotel.hasRestaurant)
        {'icon': Icons.restaurant, 'label': 'Restaurant'},
      if (widget.hotel.hasBar) {'icon': Icons.local_bar, 'label': 'Bar'},
      if (widget.hotel.hasRoomService)
        {'icon': Icons.room_service, 'label': 'Room Service'},
    ];

    if (amenities.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Amenities',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Wrap(
            spacing: AppTheme.spacingSmall,
            runSpacing: AppTheme.spacingSmall,
            children: amenities.map((amenity) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingSmall,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.primaryColor.withAlpha(26),
                      AppTheme.primaryColor.withAlpha(13),
                    ],
                  ),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusLarge),
                  border: Border.all(
                    color: AppTheme.primaryColor.withAlpha(77),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      amenity['icon'] as IconData,
                      color: AppTheme.primaryColor,
                      size: 18,
                    ),
                    const SizedBox(width: AppTheme.spacingXS),
                    Text(
                      amenity['label'] as String,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildRoomsSection() {
    if (widget.hotel.rooms.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Available Rooms',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          ...widget.hotel.rooms.take(3).map((room) => _buildRoomCard(room)),
          if (widget.hotel.rooms.length > 3)
            TextButton(
              onPressed: () {
                // Show all rooms
              },
              child: Text(
                'View all ${widget.hotel.rooms.length} rooms',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRoomCard(HotelRoom room) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.surfaceColor,
            AppTheme.surfaceVariant,
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  room.type.displayName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingSmall,
                  vertical: AppTheme.spacingXS,
                ),
                decoration: BoxDecoration(
                  gradient: AppTheme.accentGradient,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  room.formattedPricePerNight,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            room.description,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Row(
            children: [
              Icon(
                Icons.people_outline,
                size: 16,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: AppTheme.spacingXS),
              Text(
                '${room.maxGuests} guests',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMedium),
              Icon(
                Icons.bed_outlined,
                size: 16,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: AppTheme.spacingXS),
              Text(
                '${room.bedCount} ${room.bedType}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Location',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          HotelMapView(hotel: widget.hotel),
        ],
      ),
    );
  }

  Widget _buildReviewsSection() {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Reviews',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Row(
                children: [
                  const Icon(
                    Icons.star_rounded,
                    color: Color(0xFFFFB800),
                    size: 20,
                  ),
                  const SizedBox(width: AppTheme.spacingXS),
                  Text(
                    '${widget.hotel.rating.toStringAsFixed(1)} (${widget.hotel.reviewCount})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          HotelReviewList(hotel: widget.hotel),
        ],
      ),
    );
  }

  Widget _buildFloatingBookingBar() {
    return AnimatedBuilder(
      animation: _bookingScale,
      builder: (context, child) {
        return Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Transform.scale(
            scale: _bookingScale.value,
            child: Container(
              margin: const EdgeInsets.all(AppTheme.spacingMedium),
              padding: const EdgeInsets.all(AppTheme.spacingLarge),
              decoration: BoxDecoration(
                gradient: AppTheme.cardGradient,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusXLarge),
                boxShadow: AppTheme.shadowLarge,
                border: Border.all(color: AppTheme.outline, width: 0.5),
              ),
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Date and guest selection
                    Row(
                      children: [
                        Expanded(
                          child: _buildDateSelector(
                            'Check-in',
                            _checkInDate,
                            () => _selectDate(true),
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Expanded(
                          child: _buildDateSelector(
                            'Check-out',
                            _checkOutDate,
                            () => _selectDate(false),
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Expanded(
                          child: _buildGuestSelector(),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    // Booking buttons
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: ElevatedButton(
                            onPressed: () {
                              // Navigate to booking screen
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  vertical: AppTheme.spacingMedium),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                    AppTheme.borderRadiusLarge),
                              ),
                            ),
                            child: Text(
                              'Book Now - ${widget.hotel.currency}${_totalPrice.toStringAsFixed(0)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Expanded(
                          flex: 2,
                          child: InstantBookingButton(
                            travelService: widget.hotel,
                            serviceDate: _checkInDate,
                            participantCount: _guests,
                            totalAmount: _totalPrice,
                            currency: widget.hotel.currency,
                            additionalDetails: {
                              'checkInDate': _checkInDate.toIso8601String(),
                              'checkOutDate': _checkOutDate.toIso8601String(),
                              'nights': _nights,
                              'guests': _guests,
                            },
                            buttonText: 'Instant',
                            buttonIcon: Icons.flash_on,
                            buttonColor: AppTheme.accentColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateSelector(String label, DateTime date, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingSmall),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.surfaceColor,
              AppTheme.surfaceVariant,
            ],
          ),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          border: Border.all(color: AppTheme.outline, width: 0.5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: AppTheme.spacingXS),
            Text(
              DateFormat('MMM dd').format(date),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuestSelector() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.surfaceColor,
            AppTheme.surfaceVariant,
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(color: AppTheme.outline, width: 0.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Guests',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.spacingXS),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$_guests',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: _guests > 1
                        ? () {
                            setState(() {
                              _guests--;
                              _calculateTotalPrice();
                            });
                          }
                        : null,
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: _guests > 1
                            ? AppTheme.primaryColor
                            : AppTheme.surfaceVariant,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: Icon(
                        Icons.remove,
                        size: 16,
                        color: _guests > 1
                            ? Colors.white
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacingXS),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _guests++;
                        _calculateTotalPrice();
                      });
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: const Icon(
                        Icons.add,
                        size: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(bool isCheckIn) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isCheckIn ? _checkInDate : _checkOutDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isCheckIn) {
          _checkInDate = picked;
          if (_checkInDate.isAfter(_checkOutDate)) {
            _checkOutDate = _checkInDate.add(const Duration(days: 1));
          }
        } else {
          _checkOutDate = picked;
          if (_checkOutDate.isBefore(_checkInDate)) {
            _checkInDate = _checkOutDate.subtract(const Duration(days: 1));
          }
        }
        _calculateTotalPrice();
      });
    }
  }
}
