import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/widgets/experience_card.dart';
import 'package:culture_connect/screens/map_view_screen.dart';
import 'package:culture_connect/screens/experience_details_screen.dart';
import 'package:culture_connect/providers/experience_provider.dart';
import 'package:culture_connect/screens/ar_explore_screen.dart';
import 'package:culture_connect/screens/ar_tutorial_screen.dart';
import 'package:culture_connect/widgets/filter_dialog.dart';
import 'package:culture_connect/widgets/saved_experiences_section.dart';
import 'package:culture_connect/widgets/recently_viewed_section.dart';
import 'package:culture_connect/widgets/popular_experiences_section.dart';
import 'package:culture_connect/widgets/category_experiences_section.dart';
import 'package:culture_connect/models/filter_options.dart';
import 'package:culture_connect/services/error_handling_service.dart';
import 'package:culture_connect/widgets/interactive/enhanced_gesture_detector.dart';
import 'package:culture_connect/widgets/interactive/enhanced_loading_states.dart';
import 'package:culture_connect/widgets/interactive/enhanced_error_states.dart'
    as enhanced_error;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:culture_connect/theme/app_theme.dart';

class ExploreScreen extends ConsumerStatefulWidget {
  const ExploreScreen({super.key});

  @override
  ConsumerState<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends ConsumerState<ExploreScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<String> _categories = [
    'All',
    'Cultural Tours',
    'Cooking Classes',
    'Art & Craft',
    'Music & Dance',
    'Language Exchange',
    'Local Events',
    'Nature & Wildlife',
    'Food & Drink',
  ];

  bool _isSearching = false;
  bool _isLoadingMore = false;
  bool _hasMoreItems = true;
  int _currentPage = 1;
  final int _itemsPerPage = 10;
  bool _isOffline = false;
  StreamSubscription? _connectivitySubscription;
  String _searchQuery = '';
  String _selectedQuickFilter = '';

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    _checkConnectivity();
    _setupConnectivityListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  Future<void> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      setState(() {
        _isOffline = connectivityResult == ConnectivityResult.none;
      });
    } catch (e, stackTrace) {
      final errorHandlingService = ref.read(errorHandlingServiceProvider);
      await errorHandlingService.handleError(
        error: e,
        context: 'ExploreScreen._checkConnectivity',
        stackTrace: stackTrace,
        type: ErrorType.network,
        severity: ErrorSeverity.low,
      );
    }
  }

  void _setupConnectivityListener() {
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((result) {
      setState(() {
        _isOffline = result == ConnectivityResult.none;
      });

      if (!_isOffline) {
        // Refresh data when coming back online
        ref.read(experiencesProvider.notifier).refreshExperiences();
      }
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore &&
        _hasMoreItems) {
      _loadMoreItems();
    }
  }

  Future<void> _loadMoreItems() async {
    if (_isOffline) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Simulate loading more items
      await Future.delayed(const Duration(seconds: 1));

      // In a real app, you would fetch the next page of items from the API
      // For now, we'll just simulate it by checking if we've reached the end
      final filteredExperiences = ref.read(filteredExperiencesProvider);
      final totalPages = (filteredExperiences.length / _itemsPerPage).ceil();

      if (_currentPage < totalPages) {
        _currentPage++;
      } else {
        _hasMoreItems = false;
      }
    } catch (e, stackTrace) {
      final errorHandlingService = ref.read(errorHandlingServiceProvider);
      await errorHandlingService.handleError(
        error: e,
        context: 'ExploreScreen._loadMoreItems',
        stackTrace: stackTrace,
        type: ErrorType.network,
        severity: ErrorSeverity.low,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => const FilterDialog(),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchQuery = '';
      }
    });
  }

  // Modern Hero Header with enhanced search and visual appeal
  Widget _buildModernHeroHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.heroGradient,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.borderRadiusXXLarge),
          bottomRight: Radius.circular(AppTheme.borderRadiusXXLarge),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and profile
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Discover',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(77),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: AppTheme.spacingXS),
                      Text(
                        'Amazing experiences await',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withAlpha(230),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  // Profile/Menu button
                  Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(51),
                      borderRadius:
                          BorderRadius.circular(AppTheme.borderRadiusXLarge),
                      border: Border.all(
                        color: Colors.white.withAlpha(77),
                        width: 1,
                      ),
                    ),
                    child: const Icon(
                      Icons.person_outline,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppTheme.spacingLarge),
              // Enhanced Search Bar
              _buildEnhancedSearchBar(),
              const SizedBox(height: AppTheme.spacingMedium),
              // Quick Filter Chips
              _buildQuickFilterChips(),
            ],
          ),
        ),
      ),
    );
  }

  // Enhanced Search Bar with modern design
  Widget _buildEnhancedSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXXLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(
          color: Colors.white.withAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Search Icon
          Padding(
            padding: const EdgeInsets.only(left: AppTheme.spacingLarge),
            child: Icon(
              Icons.search_rounded,
              color: AppTheme.textSecondaryColor,
              size: 22,
            ),
          ),
          // Search Input
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                // Add debounced search logic here
              },
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                hintText: 'Search experiences, places...',
                hintStyle: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingMedium,
                ),
              ),
            ),
          ),
          // Voice Search Button
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingSmall),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusXLarge),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withAlpha(77),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.mic_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          // Filter Button
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingSmall),
            child: GestureDetector(
              onTap: _showFilterDialog,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: AppTheme.secondaryGradient,
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusXLarge),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.secondaryColor.withAlpha(77),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.tune_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Quick Filter Chips
  Widget _buildQuickFilterChips() {
    final quickFilters = [
      'All',
      'Popular',
      'Nearby',
      'Cultural',
      'Food',
      'Nature'
    ];

    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: quickFilters.length,
        itemBuilder: (context, index) {
          final filter = quickFilters[index];
          final isSelected = _selectedQuickFilter == filter;

          return Container(
            margin: EdgeInsets.only(
              right:
                  index < quickFilters.length - 1 ? AppTheme.spacingSmall : 0,
            ),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedQuickFilter = isSelected ? '' : filter;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacingMedium,
                  vertical: AppTheme.spacingSmall,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white : Colors.white.withAlpha(77),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusXLarge),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryColor.withAlpha(128)
                        : Colors.white.withAlpha(128),
                    width: 1,
                  ),
                  boxShadow: isSelected ? AppTheme.shadowSmall : null,
                ),
                child: Text(
                  filter,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected ? AppTheme.primaryColor : Colors.white,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Modern Category Navigation
  Widget _buildCategoryNavigation() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMedium,
        vertical: AppTheme.spacingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Title
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: AppTheme.spacingSmall),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Explore Categories',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to all categories
                  },
                  child: Text(
                    'View All',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          // Category Grid
          _buildCategoryGrid(),
        ],
      ),
    );
  }

  // Category Grid
  Widget _buildCategoryGrid() {
    final categories = [
      {
        'name': 'Cultural Tours',
        'icon': Icons.museum_outlined,
        'color': AppTheme.primaryColor
      },
      {
        'name': 'Food & Drink',
        'icon': Icons.restaurant_outlined,
        'color': AppTheme.secondaryColor
      },
      {
        'name': 'Nature',
        'icon': Icons.nature_outlined,
        'color': AppTheme.accentColor
      },
      {
        'name': 'Art & Craft',
        'icon': Icons.palette_outlined,
        'color': AppTheme.warningColor
      },
      {
        'name': 'Music & Dance',
        'icon': Icons.music_note_outlined,
        'color': AppTheme.errorColor
      },
      {
        'name': 'Local Events',
        'icon': Icons.event_outlined,
        'color': AppTheme.infoColor
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.0,
        crossAxisSpacing: AppTheme.spacingSmall,
        mainAxisSpacing: AppTheme.spacingSmall,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return GestureDetector(
          onTap: () {
            // Handle category selection
            setState(() {
              _selectedQuickFilter = category['name'] as String;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  (category['color'] as Color).withAlpha(26),
                  (category['color'] as Color).withAlpha(13),
                ],
              ),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              border: Border.all(
                color: (category['color'] as Color).withAlpha(77),
                width: 1,
              ),
              boxShadow: AppTheme.shadowSmall,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: category['color'] as Color,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                    boxShadow: [
                      BoxShadow(
                        color: (category['color'] as Color).withAlpha(77),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    category['icon'] as IconData,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(height: AppTheme.spacingSmall),
                Text(
                  category['name'] as String,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final experiencesAsync = ref.watch(experiencesProvider);
    final filterOptions = ref.watch(filterOptionsProvider);
    final filteredExperiences = ref.watch(filteredExperiencesProvider);

    // Calculate how many items to show based on current page
    final itemsToShow =
        filteredExperiences.take(_currentPage * _itemsPerPage).toList();

    return Scaffold(
      body: EnhancedRefreshIndicator(
        onRefresh: () async {
          await ref.read(experiencesProvider.notifier).refreshExperiences();
          // Reset pagination
          setState(() {
            _currentPage = 1;
            _hasMoreItems = true;
          });
        },
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // Modern Hero Header Section
            SliverToBoxAdapter(
              child: _buildModernHeroHeader(),
            ),
            // Category Navigation Section
            SliverToBoxAdapter(
              child: _buildCategoryNavigation(),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_isSearching) ...[
                      // Modern Search Bar with enhanced design
                      Container(
                        decoration: BoxDecoration(
                          gradient: AppTheme.cardGradient,
                          borderRadius: BorderRadius.circular(
                              AppTheme.borderRadiusXXLarge),
                          boxShadow: AppTheme.shadowSmall,
                          border: Border.all(
                            color: AppTheme.outline,
                            width: 0.5,
                          ),
                        ),
                        child: TextField(
                          controller: _searchController,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Where would you like to explore?',
                            hintStyle: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey[600],
                            ),
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(12),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: theme.primaryColor.withAlpha(26),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.search,
                                size: 20,
                                color: theme.primaryColor,
                              ),
                            ),
                            suffixIcon: Container(
                              margin: const EdgeInsets.all(8),
                              child: Material(
                                color: theme.primaryColor,
                                borderRadius: BorderRadius.circular(16),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(16),
                                  onTap: () {
                                    // TODO: Implement voice search
                                  },
                                  child: Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: const Icon(
                                      Icons.mic,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {});
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // Enhanced Categories with AirBnB-inspired design
                    SizedBox(
                      height: 48,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        itemCount: _categories.length,
                        itemBuilder: (context, index) {
                          final category = _categories[index];
                          final isSelected = category == filterOptions.category;
                          return Padding(
                            padding: const EdgeInsets.only(right: 12),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(24),
                                onTap: () {
                                  // Update category filter
                                  ref
                                          .read(filterOptionsProvider.notifier)
                                          .state =
                                      filterOptions.copyWith(
                                          category: category);
                                },
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? theme.primaryColor
                                        : Colors.white,
                                    borderRadius: BorderRadius.circular(24),
                                    border: Border.all(
                                      color: isSelected
                                          ? theme.primaryColor
                                          : Colors.grey.withAlpha(51),
                                      width: 1,
                                    ),
                                    boxShadow: isSelected
                                        ? [
                                            BoxShadow(
                                              color: theme.primaryColor
                                                  .withAlpha(51),
                                              blurRadius: 6,
                                              offset: const Offset(0, 2),
                                            ),
                                          ]
                                        : [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(8),
                                              blurRadius: 2,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                  ),
                                  child: Text(
                                    category,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: isSelected
                                          ? FontWeight.w600
                                          : FontWeight.w500,
                                      color: isSelected
                                          ? Colors.white
                                          : theme.textTheme.bodyMedium?.color,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),

            // Offline banner
            if (_isOffline)
              SliverToBoxAdapter(
                child: Container(
                  color: Colors.orange,
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Row(
                    children: [
                      const Icon(Icons.wifi_off, color: Colors.white),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Text(
                          'You are offline. Some features may be limited.',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      TextButton(
                        onPressed: () async {
                          await _checkConnectivity();
                          if (!_isOffline) {
                            ref
                                .read(experiencesProvider.notifier)
                                .refreshExperiences();
                          }
                        },
                        child: const Text(
                          'RETRY',
                          style: TextStyle(
                              color: Colors.white, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Experience List
            experiencesAsync.when(
              data: (experiences) {
                // Filter by search query
                var displayedExperiences = itemsToShow;
                if (_searchController.text.isNotEmpty) {
                  displayedExperiences = ref
                      .read(experiencesProvider.notifier)
                      .searchExperiences(_searchController.text);
                }

                if (displayedExperiences.isEmpty) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.search_off,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No experiences found',
                            style: theme.textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Try adjusting your filters or search terms',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              ref.read(filterOptionsProvider.notifier).state =
                                  const FilterOptions();
                              _searchController.clear();
                              setState(() {});
                            },
                            child: const Text('Clear Filters'),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return _isSearching || filterOptions.isAnyFilterApplied
                    ? _buildFilteredExperiencesGrid(displayedExperiences)
                    : _buildHomeExploreView();
              },
              loading: () => SliverFillRemaining(
                child: EnhancedListSkeletonLoader(
                  itemCount: 5,
                  itemBuilder: (index) =>
                      const EnhancedExperienceSkeletonCard(),
                ),
              ),
              error: (error, stackTrace) => SliverFillRemaining(
                child: enhanced_error.EnhancedServerError(
                  onRetry: () {
                    ref.read(experiencesProvider.notifier).refreshExperiences();
                  },
                  onGoBack: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: 'ar_button',
            onPressed: () {
              // Show a dialog to choose between tutorial and direct AR access
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('AR Experience'),
                  content: const Text(
                      'Would you like to view a tutorial on how to use the AR features, or go directly to the AR experience?'),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context); // Close dialog
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ARTutorialScreen(),
                          ),
                        );
                      },
                      child: const Text('View Tutorial'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context); // Close dialog
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ARExploreScreen(),
                          ),
                        );
                      },
                      child: const Text('Skip Tutorial'),
                    ),
                  ],
                ),
              );
            },
            backgroundColor: Theme.of(context).colorScheme.secondary,
            child: const Icon(Icons.view_in_ar),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: 'map_button',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MapViewScreen(),
                ),
              );
            },
            child: const Icon(Icons.map),
          ),
        ],
      ),
    );
  }

  Widget _buildFilteredExperiencesGrid(List<dynamic> experiences) {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == experiences.length) {
            return _isLoadingMore
                ? const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: EnhancedExperienceSkeletonCard(),
                  )
                : const SizedBox.shrink();
          }

          final experience = experiences[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SwipeableCard(
              onSwipeRight: () {
                // Add to favorites
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Added to favorites!')),
                );
              },
              onSwipeLeft: () {
                // Share experience
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Sharing experience...')),
                );
              },
              rightActionIcon: Icons.favorite,
              leftActionIcon: Icons.share,
              rightActionColor: Colors.red,
              leftActionColor: Colors.blue,
              child: ExperienceCard(
                title: experience.title,
                imageUrl: experience.imageUrl,
                rating: experience.rating,
                reviewCount: experience.reviewCount,
                price: experience.price.toString(),
                category: experience.category,
                location: experience.location,
                onTap: () {
                  ref
                      .read(experiencesProvider.notifier)
                      .markAsViewed(experience.id);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ExperienceDetailsScreen(
                        experience: experience,
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
        childCount: experiences.length + (_isLoadingMore ? 1 : 0),
      ),
    );
  }

  // Optimized: Lazy loading home explore view with better performance
  Widget _buildHomeExploreView() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // Optimized: Build sections on demand with lazy loading
          switch (index) {
            case 0:
              return const RecentlyViewedSection();
            case 1:
              return const SavedExperiencesSection();
            case 2:
              return const PopularExperiencesSection();
            case 3:
              return const CategoryExperiencesSection(
                category: 'Cultural Tours',
              );
            case 4:
              return const CategoryExperiencesSection(
                category: 'Cooking Classes',
              );
            case 5:
              return const CategoryExperiencesSection(
                category: 'Nature & Wildlife',
              );
            case 6:
              return const CategoryExperiencesSection(
                category: 'Music & Dance',
              );
            case 7:
              // Loading indicator at the bottom
              return _isLoadingMore
                  ? const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : const SizedBox.shrink();
            default:
              return null;
          }
        },
        childCount: 8, // Total number of sections + loading indicator
      ),
    );
  }
}
