import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:culture_connect/models/user_model.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/navigation_provider.dart';
import 'package:culture_connect/providers/preferences_provider.dart';
import 'package:culture_connect/providers/travel/timeline_providers.dart';
import 'package:culture_connect/screens/loyalty/loyalty_dashboard_screen.dart';
import 'package:culture_connect/screens/settings/security_settings_screen.dart';
import 'package:culture_connect/screens/travel/price_alerts_screen.dart';
import 'package:culture_connect/screens/voice_translation/voice_translation_screen.dart';
import 'package:culture_connect/services/interactive_features_service.dart'
    as interactive;
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/custom_button.dart';
import 'package:culture_connect/widgets/custom_text_field.dart';
import 'package:culture_connect/widgets/interactive/interactive_wrapper.dart';
import 'package:culture_connect/widgets/interactive/enhanced_success_animations.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isEditing = false;
  bool _isLoading = false;
  File? _profileImageFile;
  final _formKey = GlobalKey<FormState>();

  // Controllers for editable fields
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _bioController = TextEditingController();
  final _phoneController = TextEditingController();

  // Selected preferences
  List<String> _selectedLanguages = [];
  List<String> _selectedInterests = [];

  // Available options
  final List<String> _availableLanguages = [
    'English',
    'French',
    'Yoruba',
    'Igbo',
    'Hausa',
    'Swahili',
    'Zulu',
    'Xhosa',
    'Arabic',
    'Portuguese'
  ];

  final List<String> _availableInterests = [
    'Food & Cuisine',
    'History & Heritage',
    'Art & Crafts',
    'Music & Dance',
    'Nature & Wildlife',
    'Festivals & Events',
    'Architecture',
    'Local Markets',
    'Traditional Ceremonies',
    'Indigenous Knowledge'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _bioController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    final userModel = await ref.read(currentUserModelProvider.future);
    if (userModel != null) {
      // Load user preferences from the preferences provider
      final prefs = ref.read(userPreferencesProvider);

      setState(() {
        _firstNameController.text = userModel.firstName;
        _lastNameController.text = userModel.lastName;
        _bioController.text = userModel.bio ?? '';
        _phoneController.text =
            userModel.phoneNumber.replaceAll(RegExp(r'^\+\d+'), '');

        // Use preferences from provider if available, otherwise use user model data
        _selectedLanguages = prefs.languagePreferences.isNotEmpty
            ? prefs.languagePreferences
            : (userModel.languagePreferences ?? []);

        _selectedInterests = prefs.culturalInterests.isNotEmpty
            ? prefs.culturalInterests
            : (userModel.culturalInterests ?? []);
      });
    }
  }

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 800,
      maxHeight: 800,
      imageQuality: 85,
    );

    if (image != null) {
      setState(() {
        _profileImageFile = File(image.path);
      });
    }
  }

  Future<void> _pickImageWithFeedback() async {
    final interactiveService =
        ref.read(interactive.interactiveFeaturesServiceProvider);
    await interactiveService
        .triggerGestureFeedback(interactive.GestureType.longPress);
    await _pickImage();
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userModel = await ref.read(currentUserModelProvider.future);
      if (userModel == null) throw Exception('User not authenticated');

      String? profilePictureUrl;

      // Upload profile picture if changed
      if (_profileImageFile != null) {
        final storageRef = FirebaseStorage.instance
            .ref()
            .child('profile_pictures')
            .child('${userModel.id}.jpg');

        await storageRef.putFile(_profileImageFile!);
        profilePictureUrl = await storageRef.getDownloadURL();
      }

      // Update user data in Firestore
      final userData = {
        'firstName': _firstNameController.text.trim(),
        'lastName': _lastNameController.text.trim(),
        'bio': _bioController.text.trim(),
        'languagePreferences': _selectedLanguages,
        'culturalInterests': _selectedInterests,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (profilePictureUrl != null) {
        userData['profilePicture'] = profilePictureUrl;
      }

      await FirebaseFirestore.instance
          .collection('users')
          .doc(userModel.id)
          .update(userData);

      // Refresh user data in provider
      ref.invalidate(currentUserModelProvider);

      // Sync preferences with user profile
      await ref.read(userPreferencesProvider.notifier).syncWithUserProfile(
            culturalInterests: _selectedInterests,
            languagePreferences: _selectedLanguages,
          );

      setState(() {
        _isEditing = false;
        _isLoading = false;
      });

      if (mounted) {
        // Show success animation instead of snackbar
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => EnhancedSuccessDialog(
            title: 'Profile Updated!',
            message: 'Your profile has been successfully updated.',
            successType: SuccessType.profile,
            confirmButtonText: 'Continue',
            onConfirm: () {
              // Dialog will auto-close
            },
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating profile: $e')),
        );
      }
    }
  }

  void _toggleLanguage(String language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
        // Update preferences in background
        ref
            .read(userPreferencesProvider.notifier)
            .removeLanguagePreference(language);
      } else {
        _selectedLanguages.add(language);
        // Update preferences in background
        ref
            .read(userPreferencesProvider.notifier)
            .addLanguagePreference(language);
      }
    });
  }

  void _toggleInterest(String interest) {
    setState(() {
      if (_selectedInterests.contains(interest)) {
        _selectedInterests.remove(interest);
        // Update preferences in background
        ref
            .read(userPreferencesProvider.notifier)
            .removeCulturalInterest(interest);
      } else {
        _selectedInterests.add(interest);
        // Update preferences in background
        ref
            .read(userPreferencesProvider.notifier)
            .addCulturalInterest(interest);
      }
    });
  }

  void _toggleTheme() {
    final prefsNotifier = ref.read(userPreferencesProvider.notifier);
    final currentThemeMode = ref.read(themeModeProvider);
    final newThemeMode =
        currentThemeMode == ThemeMode.dark ? ThemeMode.light : ThemeMode.dark;
    prefsNotifier.setThemeMode(newThemeMode);
  }

  void _showTimelineSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Select Timeline'),
          content: SizedBox(
            width: double.maxFinite,
            child: Consumer(
              builder: (context, ref, child) {
                final timelinesAsync = ref.watch(timelinesProvider);

                return timelinesAsync.when(
                  data: (timelines) {
                    if (timelines.isEmpty) {
                      return const Center(
                        child: Text(
                            'No timelines found. Create one from an itinerary first.'),
                      );
                    }

                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: timelines.length,
                      itemBuilder: (context, index) {
                        final timeline = timelines[index];
                        return ListTile(
                          title: Text(timeline.title),
                          subtitle: Text(
                              '${timeline.startDate.toString().substring(0, 10)} - ${timeline.endDate.toString().substring(0, 10)}'),
                          onTap: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).pushNamed(
                              '/timeline',
                              arguments: {'timelineId': timeline.id},
                            );
                          },
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, stackTrace) => Center(
                    child: Text('Error loading timelines: $error'),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _signOut() async {
    try {
      await ref.read(authServiceProvider).signOut();
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error signing out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userModelAsync = ref.watch(currentUserModelProvider);
    final themeMode = ref.watch(themeModeProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    return PopScope(
      canPop: false, // Prevent back navigation that could cause logout
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Navigate to home tab instead of popping the route
          ref
              .read(navigationProvider.notifier)
              .setNavigationItem(NavigationItem.home);
        }
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: 'Profile',
          showBackButton:
              false, // Disable back button for bottom navigation screen
          actions: [
            if (!_isEditing)
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => setState(() => _isEditing = true),
              )
            else
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => setState(() => _isEditing = false),
              ),
          ],
        ),
        body: userModelAsync.when(
          data: (userModel) {
            // Debug logging to understand what data we're getting
            debugPrint('Profile screen user data: ${userModel?.toJson()}');

            if (userModel == null) {
              debugPrint('Profile screen: User model is null');
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.person_off, size: 64, color: Colors.grey),
                    SizedBox(height: 16),
                    Text('User not found'),
                    SizedBox(height: 8),
                    Text('Please try logging out and back in',
                        style: TextStyle(color: Colors.grey)),
                  ],
                ),
              );
            }

            return _buildProfileContent(userModel, isDarkMode);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) {
            // Debug logging for errors
            debugPrint('Profile screen error: $error');
            debugPrint('Profile screen stack trace: $stackTrace');

            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error loading profile: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Refresh the provider
                      ref.invalidate(currentUserModelProvider);
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          },
        ),
        bottomNavigationBar: _isEditing
            ? Padding(
                padding: const EdgeInsets.all(16),
                child: CustomButton(
                  text: 'Save Changes',
                  onPressed: _saveProfile,
                  isLoading: _isLoading,
                ),
              )
            : null,
      ),
    );
  }

  Widget _buildProfileContent(UserModel user, bool isDarkMode) {
    return Column(
      children: [
        // Profile header with image and basic info
        _buildProfileHeader(user),

        // Modern tab bar with card design
        Container(
          margin: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingMedium,
              vertical: AppTheme.spacingSmall),
          decoration: BoxDecoration(
            gradient: AppTheme.cardGradient,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            boxShadow: AppTheme.shadowSmall,
            border: Border.all(
              color: AppTheme.outline,
              width: 0.5,
            ),
          ),
          child: TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Info'),
              Tab(text: 'Preferences'),
              Tab(text: 'Settings'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.tab,
            indicator: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(26),
              borderRadius: BorderRadius.circular(16),
            ),
            labelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
            dividerColor: Colors.transparent,
          ),
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildInfoTab(user),
              _buildPreferencesTab(),
              _buildSettingsTab(isDarkMode),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileHeader(UserModel user) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacingMedium),
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(
          color: AppTheme.outline,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          // Large profile avatar with modern styling
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.accentColor,
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Container(
                    color: AppTheme.surfaceColor,
                    child: Stack(
                      children: [
                        Center(
                          child: Icon(
                            Icons.person,
                            size: 60,
                            color: Colors.grey.withAlpha(77),
                          ),
                        ),
                        // Show actual image if available
                        if (_profileImageFile != null)
                          Image.file(
                            _profileImageFile!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          )
                        else if (user.profilePicture != null)
                          CachedNetworkImage(
                            imageUrl: user.profilePicture!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorWidget: (context, url, error) =>
                                const SizedBox(),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              // Camera edit button with modern design
              if (_isEditing)
                GestureDetector(
                  onTap: _pickImageWithFeedback,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.accentColor.withAlpha(77),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      size: 20,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 24),

          // User name with modern typography
          Text(
            user.fullName.isNotEmpty ? user.fullName : 'Explorer',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          // Explorer badge with modern design
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppTheme.accentColor,
                  AppTheme.accentColor.withAlpha(204)
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.accentColor.withAlpha(51),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Text(
              'Explorer',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Travel stats with modern card design
          Container(
            padding: const EdgeInsets.all(AppTheme.spacingLarge),
            decoration: BoxDecoration(
              gradient: AppTheme.cardGradient,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
              boxShadow: AppTheme.shadowSmall,
              border: Border.all(
                color: AppTheme.outline,
                width: 0.5,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildStatItem("12", "Trips"),
                Container(
                  height: 40,
                  width: 1,
                  color: AppTheme.outline,
                ),
                _buildStatItem("8", "Countries"),
                Container(
                  height: 40,
                  width: 1,
                  color: AppTheme.outline,
                ),
                _buildStatItem("4.8", "Rating"),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.accentColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoTab(UserModel user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Personal Information section
            _buildSectionHeader('Personal Information'),
            const SizedBox(height: 16),

            // First Name
            CustomTextField(
              controller: _firstNameController,
              label: 'First Name',
              prefixIcon: Icons.person_outline,
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'First name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Last Name
            CustomTextField(
              controller: _lastNameController,
              label: 'Last Name',
              prefixIcon: Icons.person_outline,
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Last name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Bio
            CustomTextField(
              controller: _bioController,
              label: 'Bio',
              prefixIcon: Icons.info_outline,
              enabled: _isEditing,
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Phone Number
            CustomTextField(
              controller: _phoneController,
              label: 'Phone Number',
              prefixIcon: Icons.phone_outlined,
              enabled: _isEditing,
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language Preferences
          _buildSectionHeader('Language Preferences'),
          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableLanguages.map((language) {
              final isSelected = _selectedLanguages.contains(language);
              return FilterChip(
                label: Text(language),
                selected: isSelected,
                onSelected:
                    _isEditing ? (selected) => _toggleLanguage(language) : null,
                backgroundColor: Colors.grey[200],
                selectedColor: AppTheme.primaryColor.withAlpha(50),
                checkmarkColor: AppTheme.primaryColor,
                labelStyle: TextStyle(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.textPrimaryColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Cultural Interests
          _buildSectionHeader('Cultural Interests'),
          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableInterests.map((interest) {
              final isSelected = _selectedInterests.contains(interest);
              return FilterChip(
                label: Text(interest),
                selected: isSelected,
                onSelected:
                    _isEditing ? (selected) => _toggleInterest(interest) : null,
                backgroundColor: Colors.grey[200],
                selectedColor: AppTheme.primaryColor.withAlpha(50),
                checkmarkColor: AppTheme.primaryColor,
                labelStyle: TextStyle(
                  color: isSelected
                      ? AppTheme.primaryColor
                      : AppTheme.textPrimaryColor,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab(bool isDarkMode) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App Settings
          _buildSectionHeader('App Settings'),
          const SizedBox(height: 16),

          // Theme Toggle
          SwitchListTile(
            title: const Text('Dark Mode'),
            subtitle: Text(isDarkMode ? 'On' : 'Off'),
            value: isDarkMode,
            onChanged: (value) => _toggleTheme(),
            secondary: Icon(
              isDarkMode ? Icons.dark_mode : Icons.light_mode,
              color: isDarkMode ? Colors.amber : Colors.blueGrey,
            ),
          ),

          const Divider(),

          // Refresh Animations
          ListTile(
            leading: const Icon(Icons.refresh, color: Colors.green),
            title: const Text('Pull-to-Refresh Animations'),
            subtitle: const Text('Customize refresh animations'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pushNamed('/settings/refresh-animations');
            },
          ),

          const Divider(),

          // Notification Settings
          SwitchListTile(
            title: const Text('Push Notifications'),
            subtitle: const Text('Receive alerts and updates'),
            value: ref.watch(notificationsEnabledProvider),
            onChanged: (value) {
              ref
                  .read(userPreferencesProvider.notifier)
                  .setNotificationsEnabled(value);
            },
            secondary: const Icon(Icons.notifications_outlined),
          ),

          const Divider(),

          // Travel Settings
          _buildSectionHeader('Travel Settings'),
          const SizedBox(height: 16),

          // Itinerary Builder
          ListTile(
            leading: const Icon(Icons.map_outlined, color: Colors.blue),
            title: const Text('Itinerary Builder'),
            subtitle: const Text('Create AI-powered travel itineraries'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pushNamed('/itinerary/builder');
            },
          ),

          // Visual Timeline
          ListTile(
            leading: const Icon(Icons.timeline, color: Colors.purple),
            title: const Text('Visual Timeline'),
            subtitle: const Text('View your trip as a timeline with AR'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Show a dialog to select a timeline
              _showTimelineSelectionDialog(context);
            },
          ),

          // Price Alerts
          ListTile(
            leading: const Icon(Icons.notifications_active_outlined,
                color: Colors.amber),
            title: const Text('Price Alerts'),
            subtitle: const Text('Manage your travel price alerts'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PriceAlertsScreen(),
                ),
              );
            },
          ),

          // Loyalty Program
          ListTile(
            leading: const Icon(Icons.card_membership, color: Colors.purple),
            title: const Text('Loyalty Program'),
            subtitle: const Text('View rewards and redeem points'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const LoyaltyDashboardScreen(),
                ),
              );
            },
          ),

          // Voice Translation
          ListTile(
            leading: const Icon(Icons.translate, color: Colors.blue),
            title: const Text('Voice Translation'),
            subtitle: const Text('Translate speech in real-time'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const VoiceTranslationScreen(),
                ),
              );
            },
          ),

          // Offline Content
          ListTile(
            leading: const Icon(Icons.offline_bolt, color: Colors.green),
            title: const Text('Offline Content'),
            subtitle: const Text('Manage content available offline'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pushNamed('/offline/content');
            },
          ),

          const Divider(),

          // Account Settings
          _buildSectionHeader('Account Settings'),
          const SizedBox(height: 16),

          // Verification Levels
          ListTile(
            leading:
                const Icon(Icons.verified_user_outlined, color: Colors.green),
            title: const Text('Verification Levels'),
            subtitle: const Text('Manage your verification status'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pushNamed('/verification/levels');
            },
          ),

          // Change Password
          ListTile(
            leading: const Icon(Icons.lock_outline),
            title: const Text('Change Password'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to change password screen
            },
          ),

          // Change Email
          ListTile(
            leading: const Icon(Icons.email_outlined),
            title: const Text('Change Email'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // Navigate to change email screen
            },
          ),

          // Security Settings
          InteractiveWrapper(
            onTap: () async {
              final interactiveService =
                  ref.read(interactive.interactiveFeaturesServiceProvider);
              await interactiveService
                  .triggerGestureFeedback(interactive.GestureType.swipe);
              if (mounted) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SecuritySettingsScreen(),
                  ),
                );
              }
            },
            child: const ListTile(
              leading: Icon(Icons.security, color: Colors.orange),
              title: Text('Security Settings'),
              subtitle: Text('Auto-lock and authentication'),
              trailing: Icon(Icons.arrow_forward_ios, size: 16),
            ),
          ),

          const Divider(),

          // Offline Settings
          ListTile(
            leading:
                const Icon(Icons.offline_bolt_outlined, color: Colors.blue),
            title: const Text('Offline Settings'),
            subtitle: const Text('Configure sync preferences'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.of(context).pushNamed('/offline/settings');
            },
          ),

          const Divider(),

          // Sign Out
          InteractiveWrapper(
            onTap: () async {
              final interactiveService =
                  ref.read(interactive.interactiveFeaturesServiceProvider);
              await interactiveService
                  .triggerStateFeedback(interactive.StateType.warning);
              await _signOut();
            },
            buttonType: interactive.ButtonType.destructive,
            child: const ListTile(
              leading: Icon(Icons.logout, color: Colors.red),
              title: Text('Sign Out', style: TextStyle(color: Colors.red)),
            ),
          ),

          // Delete Account
          ListTile(
            leading: const Icon(Icons.delete_outline, color: Colors.red),
            title: const Text('Delete Account',
                style: TextStyle(color: Colors.red)),
            onTap: () {
              // Show delete account confirmation
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Delete Account'),
                  content: const Text(
                    'Are you sure you want to delete your account? This action cannot be undone.',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        // Delete account logic
                        Navigator.pop(context);
                      },
                      child: const Text('Delete',
                          style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 24,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8),
            blurRadius: 4,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              size: 16,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernInfoCard({
    required IconData icon,
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: AppTheme.cardGradient,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowSmall,
        border: Border.all(
          color: AppTheme.outline,
          width: 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingSmall),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppTheme.accentColor.withAlpha(51),
                        AppTheme.accentColor.withAlpha(26),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Icon(
                    icon,
                    color: AppTheme.accentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildModernInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Unknown';
    }
  }
}
