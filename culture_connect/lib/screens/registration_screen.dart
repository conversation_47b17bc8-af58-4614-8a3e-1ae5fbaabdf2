import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_text_field.dart';
import 'package:culture_connect/widgets/auth_gradient_background.dart';
import 'package:culture_connect/widgets/curved_content_container.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/utils/validation_utils.dart';
import 'package:intl/intl.dart';

enum RegistrationStep {
  personalInfo,
  contactInfo,
  security,
  termsAndConditions,
}

class RegistrationScreen extends ConsumerStatefulWidget {
  const RegistrationScreen({super.key});

  @override
  ConsumerState<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends ConsumerState<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _dobController = TextEditingController();

  String? _errorMessage;
  bool _isLoading = false;
  String _selectedCountryCode = '+234'; // Default to Nigeria
  DateTime? _selectedDate;
  bool _acceptTerms = false;
  RegistrationStep _currentStep = RegistrationStep.personalInfo;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  final PageController _pageController = PageController();

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _dobController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  // PRODUCTION-GRADE: Centralized validation with enhanced security
  String? _validateEmail(String? value) {
    // SECURITY: Trim and sanitize input
    final trimmedValue = value?.trim();
    return ValidationUtils.validateEmail(trimmedValue);
  }

  String? _validatePassword(String? value) {
    return ValidationUtils.validatePassword(value);
  }

  String? _validateConfirmPassword(String? value) {
    return ValidationUtils.validatePasswordConfirmation(
        value, _passwordController.text);
  }

  String? _validateName(String? value) {
    // SECURITY: Enhanced name validation with length and character constraints
    final trimmedValue = value?.trim();
    if (trimmedValue == null || trimmedValue.isEmpty) {
      return 'This field is required';
    }
    if (trimmedValue.length < 2) {
      return 'Name must be at least 2 characters';
    }
    if (trimmedValue.length > 50) {
      return 'Name must be no more than 50 characters';
    }
    // SECURITY: Check for invalid characters (allow letters, spaces, hyphens, apostrophes, dots)
    if (!RegExp(r"^[a-zA-Z\s\-\.']+$").hasMatch(trimmedValue)) {
      return 'Name contains invalid characters';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    return ValidationUtils.validatePhone(value);
  }

  String? _validateDob(String? value) {
    // PRODUCTION-GRADE: Enhanced date of birth validation
    if (value == null || value.isEmpty) {
      return 'Date of birth is required';
    }

    try {
      final date = DateFormat('yyyy-MM-dd').parse(value);
      final now = DateTime.now();
      final age = now.year -
          date.year -
          (now.month < date.month ||
                  (now.month == date.month && now.day < date.day)
              ? 1
              : 0);

      // BUSINESS RULE: Minimum age requirement (13 years)
      if (age < 13) {
        return 'You must be at least 13 years old to register';
      }

      // BUSINESS RULE: Maximum age validation (120 years)
      if (age > 120) {
        return 'Please enter a valid date of birth';
      }

      // SECURITY: Prevent future dates
      if (date.isAfter(now)) {
        return 'Date of birth cannot be in the future';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }

  // Date picker
  Future<void> _selectDate(BuildContext context) async {
    final DateTime now = DateTime.now();
    final DateTime initialDate = DateTime(now.year - 18, now.month, now.day);
    final DateTime firstDate = DateTime(now.year - 100);
    final DateTime lastDate = DateTime(now.year - 13, now.month, now.day);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppTheme.primaryColor,
              onPrimary: Colors.white,
              onSurface: AppTheme.textPrimaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dobController.text = DateFormat('dd/MM/yyyy').format(picked);
      });
    }
  }

  // Country code selection
  void _showCountryCodePicker() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Select Country Code',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: [
                    _buildCountryCodeTile('+234', 'Nigeria'),
                    _buildCountryCodeTile('+233', 'Ghana'),
                    _buildCountryCodeTile('+27', 'South Africa'),
                    _buildCountryCodeTile('+254', 'Kenya'),
                    _buildCountryCodeTile('+20', 'Egypt'),
                    _buildCountryCodeTile('+251', 'Ethiopia'),
                    _buildCountryCodeTile('+212', 'Morocco'),
                    _buildCountryCodeTile('+216', 'Tunisia'),
                    _buildCountryCodeTile('+256', 'Uganda'),
                    _buildCountryCodeTile('+255', 'Tanzania'),
                    _buildCountryCodeTile('+1', 'United States'),
                    _buildCountryCodeTile('+44', 'United Kingdom'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCountryCodeTile(String code, String country) {
    return ListTile(
      title: Text(country),
      trailing: Text(
        code,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
      ),
      onTap: () {
        setState(() {
          _selectedCountryCode = code;
        });
        Navigator.pop(context);
      },
      selected: _selectedCountryCode == code,
      selectedTileColor: AppTheme.primaryColor.withAlpha(25),
    );
  }

  // Registration with email and password
  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_acceptTerms) {
      setState(() {
        _errorMessage = 'You must accept the Terms and Conditions to continue';
      });
      return;
    }

    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });

    try {
      // SECURITY: Sanitize and validate all inputs before registration
      final sanitizedEmail = _emailController.text.trim().toLowerCase();
      final sanitizedFirstName = _firstNameController.text.trim();
      final sanitizedLastName = _lastNameController.text.trim();
      final sanitizedPhone =
          _phoneController.text.trim().replaceAll(RegExp(r'[^\d]'), '');
      final sanitizedDob = _dobController.text.trim();

      // PRODUCTION-GRADE: Final validation before submission
      if (sanitizedEmail.isEmpty ||
          sanitizedFirstName.isEmpty ||
          sanitizedLastName.isEmpty ||
          sanitizedPhone.isEmpty ||
          sanitizedDob.isEmpty) {
        setState(() {
          _errorMessage = 'All fields are required';
          _isLoading = false;
        });
        return;
      }

      // Use the auth provider to register with sanitized data
      await ref.read(authStateProvider.notifier).registerWithEmailAndPassword(
            email: sanitizedEmail,
            password: _passwordController
                .text, // Don't trim password to preserve intentional spaces
            firstName: sanitizedFirstName,
            lastName: sanitizedLastName,
            phoneNumber: '$_selectedCountryCode$sanitizedPhone',
            dateOfBirth: sanitizedDob,
          );

      // Navigate to verification screen
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/verify');
      }
    } catch (e) {
      setState(() {
        // SECURITY: Don't expose internal error details to user
        _errorMessage = e.toString().contains('email-already-in-use')
            ? 'An account with this email already exists'
            : e.toString().contains('weak-password')
                ? 'Password is too weak'
                : e.toString().contains('invalid-email')
                    ? 'Please enter a valid email address'
                    : 'Registration failed. Please try again.';
        _isLoading = false;
      });
    }
  }

  // Navigation between steps
  void _nextStep() {
    if (!mounted) return;

    if (_currentStep == RegistrationStep.termsAndConditions) {
      _register();
      return;
    }

    // Validate current step before proceeding
    if (_currentStep == RegistrationStep.personalInfo) {
      if (_firstNameController.text.isEmpty ||
          _lastNameController.text.isEmpty ||
          _dobController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
    } else if (_currentStep == RegistrationStep.contactInfo) {
      if (_emailController.text.isEmpty || _phoneController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
      if (_validateEmail(_emailController.text) != null) {
        setState(() {
          _errorMessage = 'Please enter a valid email address';
        });
        return;
      }
      if (_validatePhone(_phoneController.text) != null) {
        setState(() {
          _errorMessage = 'Please enter a valid phone number';
        });
        return;
      }
    } else if (_currentStep == RegistrationStep.security) {
      if (_passwordController.text.isEmpty ||
          _confirmPasswordController.text.isEmpty) {
        setState(() {
          _errorMessage = 'Please fill in all required fields';
        });
        return;
      }
      if (_validatePassword(_passwordController.text) != null) {
        setState(() {
          _errorMessage = _validatePassword(_passwordController.text);
        });
        return;
      }
      if (_validateConfirmPassword(_confirmPasswordController.text) != null) {
        setState(() {
          _errorMessage = 'Passwords do not match';
        });
        return;
      }
    }

    setState(() {
      _errorMessage = null;
      _currentStep = RegistrationStep.values[_currentStep.index + 1];
    });

    _pageController.animateToPage(
      _currentStep.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousStep() {
    if (!mounted) return;

    if (_currentStep == RegistrationStep.personalInfo) {
      Navigator.pop(context);
      return;
    }

    setState(() {
      _errorMessage = null;
      _currentStep = RegistrationStep.values[_currentStep.index - 1];
    });

    _pageController.animateToPage(
      _currentStep.index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  // UI Components

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 50,
          height: 3,
          decoration: const BoxDecoration(
            color: AppTheme.authButtonColor,
            borderRadius: BorderRadius.all(Radius.circular(2)),
          ),
        ),
      ],
    );
  }

  // Step-specific UI components

  Widget _buildScrollableStep(Widget child) {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: child,
    );
  }

  Widget _buildPersonalInfoStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Personal Information'),
        const SizedBox(height: 24),
        CustomTextField(
          controller: _firstNameController,
          label: 'First Name',
          prefixIcon: Icons.person_outline,
          validator: _validateName,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _lastNameController,
          label: 'Last Name',
          prefixIcon: Icons.person_outline,
          validator: _validateName,
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () => _selectDate(context),
          child: AbsorbPointer(
            child: CustomTextField(
              controller: _dobController,
              label: 'Date of Birth',
              prefixIcon: Icons.calendar_today,
              validator: _validateDob,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfoStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Contact Information'),
        const SizedBox(height: 24),
        CustomTextField(
          controller: _emailController,
          label: 'Email Address',
          prefixIcon: Icons.email_outlined,
          keyboardType: TextInputType.emailAddress,
          validator: _validateEmail,
        ),
        const SizedBox(height: 16),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Phone Number label
            const Text(
              'Phone Number',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            // Phone input row
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Country code selector
                GestureDetector(
                  onTap: _showCountryCodePicker,
                  child: Container(
                    height: 56,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppTheme.dividerColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _selectedCountryCode,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: AppTheme.textSecondaryColor,
                          size: 20,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Phone number input
                Expanded(
                  child: SizedBox(
                    height: 56,
                    child: TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      validator: _validatePhone,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppTheme.textPrimaryColor,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Enter phone number',
                        hintStyle: const TextStyle(
                          color: AppTheme.textSecondaryColor,
                          fontSize: 16,
                        ),
                        filled: true,
                        fillColor: AppTheme.surfaceColor,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 16,
                        ),
                        prefixIcon: const Icon(
                          Icons.phone_outlined,
                          color: AppTheme.textSecondaryColor,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: AppTheme.primaryColor,
                            width: 2,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: AppTheme.errorColor,
                            width: 2,
                          ),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                            color: AppTheme.errorColor,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSecurityStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Security'),
        const SizedBox(height: 24),
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscurePassword,
          validator: _validatePassword,
          suffix: IconButton(
            icon: Icon(
              _obscurePassword
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _confirmPasswordController,
          label: 'Confirm Password',
          prefixIcon: Icons.lock_outline,
          obscureText: _obscureConfirmPassword,
          validator: _validateConfirmPassword,
          suffix: IconButton(
            icon: Icon(
              _obscureConfirmPassword
                  ? Icons.visibility_outlined
                  : Icons.visibility_off_outlined,
              color: AppTheme.textSecondaryColor,
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Password must contain:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 8),
        _buildPasswordRequirement('At least 8 characters'),
        _buildPasswordRequirement('At least one uppercase letter'),
        _buildPasswordRequirement('At least one number'),
        _buildPasswordRequirement('At least one special character'),
      ],
    );
  }

  Widget _buildPasswordRequirement(String requirement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle_outline,
            size: 16,
            color: AppTheme.successColor,
          ),
          const SizedBox(width: 8),
          Text(
            requirement,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Terms & Conditions'),
        const SizedBox(height: 24),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8)),
            border: Border.fromBorderSide(BorderSide(
              color: AppTheme.dividerColor,
              width: 1,
            )),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'By creating an account, you agree to our:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 16),
              _buildTermsLink('Terms of Service'),
              _buildTermsLink('Privacy Policy'),
              _buildTermsLink('Community Guidelines'),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: _acceptTerms,
                    onChanged: (value) {
                      setState(() {
                        _acceptTerms = value ?? false;
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                  ),
                  const Expanded(
                    child: Text(
                      'I agree to the Terms of Service, Privacy Policy, and Community Guidelines',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTermsLink(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const Icon(
            Icons.arrow_right,
            size: 20,
            color: AppTheme.authButtonColor,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.authButtonColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
    );
  }

  void _handleBackNavigation() {
    if (_currentStep == RegistrationStep.personalInfo) {
      // Navigate back to onboarding screen
      Navigator.of(context).pop();
    } else {
      // Go to previous step
      _previousStep();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _handleBackNavigation();
        }
      },
      child: AuthGradientBackground(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _handleBackNavigation,
            ),
            title: const Text(
              'Create Account',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
          ),
          body: Column(
            children: [
              // Progress indicator
              Container(
                margin:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                child: LinearProgressIndicator(
                  value:
                      (_currentStep.index + 1) / RegistrationStep.values.length,
                  backgroundColor: Colors.white.withAlpha(77),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                  minHeight: 4,
                ),
              ),

              // Step indicator
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  'Step ${_currentStep.index + 1} of ${RegistrationStep.values.length}',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Main content with curved container
              Expanded(
                child: SimpleCurvedContainer(
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        // Error message
                        if (_errorMessage != null) ...[
                          Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.red.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: AppTheme.errorColor.withAlpha(77),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.error_outline,
                                  color: AppTheme.errorColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style: const TextStyle(
                                      color: AppTheme.errorColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],

                        // Step content with proper scrolling
                        Expanded(
                          child: PageView(
                            controller: _pageController,
                            physics: const NeverScrollableScrollPhysics(),
                            children: [
                              _buildScrollableStep(_buildPersonalInfoStep()),
                              _buildScrollableStep(_buildContactInfoStep()),
                              _buildScrollableStep(_buildSecurityStep()),
                              _buildScrollableStep(_buildTermsStep()),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Bottom buttons
              Container(
                padding: const EdgeInsets.all(24),
                decoration: const BoxDecoration(
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    if (_currentStep != RegistrationStep.personalInfo) ...[
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _previousStep,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                                color: AppTheme.authButtonColor),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: const RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(16)),
                            ),
                          ),
                          child: const Text(
                            'Back',
                            style: TextStyle(
                              color: AppTheme.authButtonColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                    ],
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _nextStep,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              vertical: AppTheme.spacingMedium),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                AppTheme.borderRadiusLarge),
                          ),
                          elevation: 0,
                          shadowColor: AppTheme.primaryColor.withAlpha(77),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : Text(
                                _currentStep ==
                                        RegistrationStep.termsAndConditions
                                    ? 'Create Account'
                                    : 'Next',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
