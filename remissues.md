flutter: #39     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #40     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #44     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #45     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #46     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #47     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #48     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #49     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #50     PaintingContext.pushLayer (package:flutter/src/rendering/object.dart:497:12)
flutter: #51     PaintingContext.pushClipRect (package:flutter/src/rendering/object.dart:557:7)
flutter: #52     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:740:38)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #55     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #56     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #57     PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #58     RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #59     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #60     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #61     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #62     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #63     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #64     _invoke (dart:ui/hooks.dart:312:13)
flutter: #65     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #66     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:06.616451] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.649574] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.698850] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.732368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.799044] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.848837] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.903906] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.949193] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:06.998607] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.054091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.099437] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.149001] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.198983] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.282519] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.315526] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.365452] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.432947] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.465701] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.516493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.582446] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:07.615710] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.419065] [PerformanceMonitoringService] Slow frame detected {"duration_ms":45}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.448862] [PerformanceMonitoringService] Slow frame detected {"duration_ms":38}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.482591] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.715312] [PerformanceMonitoringService] Slow frame detected {"duration_ms":233}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.782088] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.833925] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.866683] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:08.899215] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:49:13.909847] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.482012] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.533091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.600887] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.667422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.751539] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.815712] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.916311] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:49:16.999020] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:17.065380] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:17.132409] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:17.215459] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:17.433149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:17.472645] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:18.073270] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:18.172917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":41}
flutter: 🐛 DEBUG [2025-07-09T16:49:18.236189] [FlightSearchService] Initializing flight search service
flutter: 🐛 DEBUG [2025-07-09T16:49:19.439418] [FlightSearchService] Flight search service initialized with 600 flights, 12 airports, and 8 airlines
flutter: 🐛 DEBUG [2025-07-09T16:49:20.482084] [PerformanceMonitoringService] Slow frame detected {"duration_ms":2308}
flutter: 🐛 DEBUG [2025-07-09T16:49:20.798778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":316}
flutter: 🐛 DEBUG [2025-07-09T16:49:20.832909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:20.866303] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:20.932043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:21.216230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:22.182602] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.498822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.616935] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.658810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.716018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.757748] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.815290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.882700] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.932119] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.998778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.048813] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.088385] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.127059] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.544586] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.566208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.833135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.865486] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.915996] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.982448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.049043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.082429] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.165526] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.215569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.282430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.332186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.366576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.415606] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-09T16:49:28.909854] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.650314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.696149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.749494] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.799182] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.866444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.915597] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.949766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.999625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.066275] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.132164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.215717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.248684] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.322839] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.382284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.449113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.548884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.599055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.665487] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.715900] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.749094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.798802] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.465917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.498858] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.567005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.632790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.682211] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.748646] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.816210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.865619] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.899846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.949294] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.716219] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.748851] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.832146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.882293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.949993] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.998660] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.066026] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.115630] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.182474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.249546] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.315734] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.349118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.382471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.483077] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.532346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.583069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.649362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.699548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.782909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.848866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.915555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.966063] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.999036] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:36.049054] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.716918] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.793338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.950605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":155}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.048841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.115643] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.262682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":147}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.282849] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.332711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.400949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.515196] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:49:43.919750] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-07-09T16:49:44.083175] [TravelServicesMockData] Generating mock data for travel services
flutter: 🐛 DEBUG [2025-07-09T16:49:44.667608] [TravelServicesMockData] Successfully generated mock data for travel services
flutter: 🐛 DEBUG [2025-07-09T16:49:44.698598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":700}
flutter: ❌ ERROR [2025-07-09T16:49:44.865628] [FlutterError] A RenderFlex overflowed by 40 pixels on the right. A RenderFlex overflowed by 40 pixels on the right.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #10     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #11     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #12     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #13     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #17     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #18     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #19     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #20     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #21     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #22     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #23     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #44     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #45     PaintingContext.pushClipPath.<anonymous closure> (package:flutter/src/rendering/object.dart:638:81)
flutter: #46     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #47     ClipContext.clipPathAndPaint (package:flutter/src/painting/clip.dart:40:5)
flutter: #48     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:638:7)
flutter: #49     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #61     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #62     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #74     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:750:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #77     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #78     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #96     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #97     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #98     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #99     RenderAnimatedOpacityMixin.paint (package:flutter/src/rendering/proxy_box.dart:1091:11)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #102    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #103    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #105    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #106    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #107    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #111    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #120    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #121    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #129    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #130    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #131    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #137    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #138    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #142    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #156    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #157    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #163    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #170    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #182    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #183    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #184    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #185    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #186    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #187    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #188    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #202    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #203    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #204    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #205    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #206    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #207    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #208    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #209    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #210    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #211    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #212    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #213    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #214    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #215    _invoke (dart:ui/hooks.dart:312:13)
flutter: #216    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #217    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:45.082191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":383}
flutter: 🐛 DEBUG [2025-07-09T16:49:45.132207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:45.182304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:46.152066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":136}
flutter: 🐛 DEBUG [2025-07-09T16:49:47.704789] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-09T16:49:47.733030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-09T16:49:49.799106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:49.848900] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.349040] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.399257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.456166] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-09T16:49:52.513371] [HotelDetailsScreenEnhanced] Error initializing hotel details screen _HotelDetailsScreenEnhancedState is a SingleTickerProviderStateMixin but multiple tickers were created.
flutter: A SingleTickerProviderStateMixin can only be used as a TickerProvider once.
flutter: If a State is used for multiple AnimationController objects, or if it is passed to other objects and those objects might use it more than one time in total, then instead of mixing in a SingleTickerProviderStateMixin, use a regular TickerProviderStateMixin.
flutter: Stack trace:
flutter: #0      SingleTickerProviderStateMixin.createTicker.<anonymous closure> (package:flutter/src/widgets/ticker_provider.dart:190:7)
flutter: #1      SingleTickerProviderStateMixin.createTicker (package:flutter/src/widgets/ticker_provider.dart:199:6)
flutter: #2      new AnimationController (package:flutter/src/animation/animation_controller.dart:261:21)
flutter: #3      _HotelDetailsScreenEnhancedState.initState (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:64:30)
flutter: #4      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5762:55)
flutter: #5      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #6      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #7      Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #8      SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #9      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #14     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #15     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #16     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #17     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #18     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #19     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #20     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #21     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #27     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #28     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #29     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #30     MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #31     MultiChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:7061:32)
flutter: #32     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #33     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #34     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #35     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #36     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #37     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #38     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #39     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #40     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #41     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #42     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #43     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #44     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #45     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #46     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #47     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #48     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #49     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #50     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #51     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #52     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #53     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #54     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #55     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #56     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #57     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #58     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #59     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #60     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #61     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #62     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #63     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #64     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #65     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #66     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #67     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #68     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #69     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #70     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #71     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #72     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #73     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #74     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #75     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #76     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #77     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #78     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #79     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #80     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #81     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #82     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #83     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #84     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #85     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #86     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #87     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #88     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #89     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #90     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #91     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #92     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #93     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #94     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #95     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #96     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #97     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #98     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #99     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #100    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #101    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #102    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #103    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #104    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #105    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #106    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #107    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #108    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #109    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #110    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #111    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #112    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #113    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #114    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #115    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #116    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #117    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #118    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #119    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #120    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #121    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #122    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #123    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #124    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #125    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #126    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #127    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #128    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #129    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #130    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #131    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #132    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #133    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #134    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #135    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #136    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #137    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #138    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #139    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #140    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #141    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #142    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #143    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #144    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #145    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #146    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #147    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #148    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #149    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #150    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #151    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #152    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #153    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #154    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #155    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #156    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #157    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #158    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #159    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #160    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #161    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #162    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #163    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #164    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #165    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #166    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #167    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #168    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #169    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #170    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #171    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #172    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #173    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #174    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #175    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #176    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #177    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #178    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #179    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #180    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #181    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #182    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #183    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #184    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #185    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #186    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #187    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #188    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #189    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #190    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #191    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #192    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #193    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #194    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #195    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #196    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #197    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #198    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #199    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #200    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #201    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #202    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #203    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #204    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #205    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #206    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #207    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #208    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #209    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #210    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #211    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #212    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #213    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #214    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #215    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #216    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #217    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #218    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #219    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #220    MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #221    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #222    Element.updateChildren (package:flutter/src/widgets/framework.dart:4150:32)
flutter: #223    MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #224    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #225    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #226    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #227    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #228    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #229    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #230    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #231    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #232    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #233    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #234    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #235    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #236    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #237    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #238    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #239    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #240    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #241    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #242    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #243    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #244    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #245    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #246    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #247    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #248    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #249    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #250    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #251    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #252    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #253    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #254    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #255    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #256    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #257    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #258    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #259    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #260    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #261    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #262    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #263    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #264    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #265    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #266    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #267    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #268    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #269    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #270    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #271    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #272    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #273    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #274    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #275    BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #276    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #277    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #278    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #279    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #280    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #281    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #282    _invoke (dart:ui/hooks.dart:312:13)
flutter: #283    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #284    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-09T16:49:52.734906] [FlutterError] LateInitializationError: Field '_fadeAnimation@*********' has not been initialized. LateInitializationError: Field '_fadeAnimation@*********' has not been initialized.
flutter: Stack trace:
flutter: #0      _HotelDetailsScreenEnhancedState._fadeAnimation (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart)
flutter: #1      _HotelDetailsScreenEnhancedState.build (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:184:18)
flutter: #2      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #3      ConsumerStatefulElement.build (package:flutter_riverpod/src/consumer.dart:539:20)
flutter: #4      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #5      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #6      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #7      ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #8      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #9      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #10     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #11     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #12     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #13     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #15     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #16     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #17     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #18     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #19     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #21     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #22     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #23     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #24     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #25     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #26     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #27     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #28     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #29     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #30     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #31     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #32     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #33     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #34     MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #35     MultiChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:7061:32)
flutter: #36     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #37     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #38     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #39     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #40     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #41     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #42     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #43     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #44     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #45     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #46     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #47     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #48     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #49     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #50     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #51     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #52     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #53     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #54     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #55     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #56     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #57     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #58     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #59     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #60     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #61     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #62     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #63     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #64     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #65     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #66     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #67     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #68     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #69     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #70     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #71     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #72     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #73     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #74     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #75     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #76     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #77     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #78     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #79     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #80     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #81     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #82     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #83     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #84     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #85     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #86     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #87     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #88     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #89     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #90     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #91     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #92     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #93     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #94     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #95     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #96     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #97     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #98     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #99     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #100    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #101    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #102    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #103    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #104    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #105    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #106    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #107    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #108    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #109    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #110    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #111    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #112    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #113    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #114    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #115    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #116    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #117    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #118    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #119    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #120    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #121    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #122    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #123    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #124    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #125    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #126    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #127    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #128    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #129    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #130    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #131    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #132    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #133    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #134    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #135    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #136    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #137    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #138    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #139    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #140    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #141    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #142    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #143    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #144    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #145    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #146    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #147    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #148    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #149    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #150    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #151    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #152    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #153    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #154    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #155    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #156    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #157    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #158    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #159    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #160    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #161    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #162    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #163    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #164    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #165    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #166    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #167    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #168    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #169    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #170    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #171    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #172    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #173    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #174    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #175    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #176    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #177    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #178    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #179    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #180    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #181    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #182    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #183    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #184    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #185    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #186    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #187    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #188    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #189    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #190    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #191    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #192    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #193    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #194    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #195    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #196    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #197    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #198    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #199    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #200    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #201    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #202    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #203    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #204    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #205    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #206    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #207    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #208    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #209    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #210    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #211    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #212    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #213    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #214    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #215    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #216    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #217    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #218    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #219    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #220    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #221    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #222    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #223    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #224    MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #225    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #226    Element.updateChildren (package:flutter/src/widgets/framework.dart:4150:32)
flutter: #227    MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #228    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #229    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #230    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #231    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #232    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #233    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #234    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #235    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #236    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #237    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #238    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #239    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #240    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #241    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #242    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #243    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #244    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #245    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #246    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #247    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #248    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #249    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #250    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #251    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #252    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #253    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #254    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #255    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #256    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #257    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #258    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #259    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #260    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #261    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #262    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #263    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #264    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #265    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #266    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #267    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #268    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #269    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #270    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #271    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #272    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #273    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #274    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #275    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #276    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #277    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #278    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #279    BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #280    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #281    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #282    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #283    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #284    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #285    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #286    _invoke (dart:ui/hooks.dart:312:13)
flutter: #287    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #288    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:52.774699] [PerformanceMonitoringService] Slow frame detected {"duration_ms":326}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.866576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":90}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.932580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-09T16:49:53.142103] [FlutterError] HTTP request failed, statusCode: 404, https://example.com/hotels/lagos_luxury.jpg HTTP request failed, statusCode: 404, https://example.com/hotels/lagos_luxury.jpg
flutter: Stack trace:
flutter: #0      NetworkImage._loadAsync (package:flutter/src/painting/_network_image_io.dart:115:9)
flutter: <asynchronous suspension>
flutter: #1      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1013:3)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:53.867344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":185}
flutter: 🐛 DEBUG [2025-07-09T16:49:55.045580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":179}
flutter: 🐛 DEBUG [2025-07-09T16:49:55.065591] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-09T16:49:56.196354] [PerformanceMonitoringService] Slow frame detected {"duration_ms":163}
flutter: 🐛 DEBUG [2025-07-09T16:49:56.215520] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:49:57.222444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-09T16:49:57.249441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: ⚠️ WARNING [2025-07-09T16:49:58.912111] [PerformanceMonitoringService] High memory usage detected {"memory_mb":161.0}
flutter: 🐛 DEBUG [2025-07-09T16:50:00.812176] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-09T16:50:00.832231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:50:04.665326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.182038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.716368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.816319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.866284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:06.648594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:06.783506] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:50:11.965492] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:12.201727] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:50:13.916641] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:13.998747] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:15.923928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-09T16:50:15.949044] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-09T16:50:17.530598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-09T16:50:17.557020] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}


flutter: 🐛 DEBUG [2025-07-09T16:49:21.216230] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:22.182602] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.498822] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.616935] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.658810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.716018] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.757748] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.815290] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.882700] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.932119] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:24.998778] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.048813] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.088385] [PerformanceMonitoringService] Slow frame detected {"duration_ms":39}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.127059] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.544586] [PerformanceMonitoringService] Slow frame detected {"duration_ms":62}
flutter: 🐛 DEBUG [2025-07-09T16:49:25.566208] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.833135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.865486] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.915996] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:26.982448] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.049043] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.082429] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.165526] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.215569] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.282430] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.332186] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.366576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:27.415606] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-09T16:49:28.909854] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.650314] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.696149] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.749494] [PerformanceMonitoringService] Slow frame detected {"duration_ms":52}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.799182] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.866444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.915597] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.949766] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:29.999625] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.066275] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.132164] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.215717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.248684] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.322839] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.382284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.449113] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.548884] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.599055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.665487] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.715900] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.749094] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:30.798802] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.465917] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.498858] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.567005] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.632790] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.682211] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.748646] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.816210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.865619] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.899846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:32.949294] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.716219] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.748851] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.832146] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.882293] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.949993] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:34.998660] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.066026] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.115630] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.182474] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.249546] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.315734] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.349118] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.382471] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.483077] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.532346] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.583069] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.649362] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.699548] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.782909] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.848866] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.915555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.966063] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:49:35.999036] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:36.049054] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.716918] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.793338] [PerformanceMonitoringService] Slow frame detected {"duration_ms":44}
flutter: 🐛 DEBUG [2025-07-09T16:49:41.950605] [PerformanceMonitoringService] Slow frame detected {"duration_ms":155}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.048841] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.115643] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.262682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":147}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.282849] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.332711] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.400949] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:49:42.515196] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:49:43.919750] [PerformanceMonitoringService] High memory usage detected {"memory_mb":169.0}
flutter: 🐛 DEBUG [2025-07-09T16:49:44.083175] [TravelServicesMockData] Generating mock data for travel services
flutter: 🐛 DEBUG [2025-07-09T16:49:44.667608] [TravelServicesMockData] Successfully generated mock data for travel services
flutter: 🐛 DEBUG [2025-07-09T16:49:44.698598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":700}
flutter: ❌ ERROR [2025-07-09T16:49:44.865628] [FlutterError] A RenderFlex overflowed by 40 pixels on the right. A RenderFlex overflowed by 40 pixels on the right.
flutter: Stack trace:
flutter: #0      LoggingService.error (package:culture_connect/services/logging_service.dart:266:72)
flutter: #1      CrashReportingService._handleFlutterError (package:culture_connect/services/crash_reporting_service.dart:93:21)
flutter: #2      FlutterError.reportError (package:flutter/src/foundation/assertions.dart:1180:14)
flutter: #3      DebugOverflowIndicatorMixin._reportOverflow (package:flutter/src/rendering/debug_overflow_indicator.dart:246:18)
flutter: #4      DebugOverflowIndicatorMixin.paintOverflowIndicator (package:flutter/src/rendering/debug_overflow_indicator.dart:314:7)
flutter: #5      RenderFlex.paint.<anonymous closure> (package:flutter/src/rendering/flex.dart:1215:7)
flutter: #6      RenderFlex.paint (package:flutter/src/rendering/flex.dart:1217:6)
flutter: #7      RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #8      PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #9      RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #10     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #11     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #12     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #13     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #14     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #15     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #16     RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #17     RenderFlex.paint (package:flutter/src/rendering/flex.dart:1166:7)
flutter: #18     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #19     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #20     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #21     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #22     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #23     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #24     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #25     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #26     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #27     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #28     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #29     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #30     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #31     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #32     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #33     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #34     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #35     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #36     _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #37     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #38     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #39     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #40     RenderCustomPaint.paint (package:flutter/src/rendering/custom_paint.dart:636:11)
flutter: #41     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #42     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #43     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #44     RenderPhysicalShape.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2196:15)
flutter: #45     PaintingContext.pushClipPath.<anonymous closure> (package:flutter/src/rendering/object.dart:638:81)
flutter: #46     ClipContext._clipAndPaint (package:flutter/src/painting/clip.dart:28:12)
flutter: #47     ClipContext.clipPathAndPaint (package:flutter/src/painting/clip.dart:40:5)
flutter: #48     PaintingContext.pushClipPath (package:flutter/src/rendering/object.dart:638:7)
flutter: #49     RenderPhysicalShape.paint (package:flutter/src/rendering/proxy_box.dart:2183:21)
flutter: #50     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #51     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #52     RenderShiftedBox.paint (package:flutter/src/rendering/shifted_box.dart:81:15)
flutter: #53     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #54     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #55     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #56     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #57     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #58     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #59     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #60     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #61     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #62     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #63     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #64     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #65     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #66     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #67     RenderSliverMultiBoxAdaptor.paint (package:flutter/src/rendering/sliver_multi_box_adaptor.dart:712:17)
flutter: #68     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #69     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #70     RenderSliverEdgeInsetsPadding.paint (package:flutter/src/rendering/sliver_padding.dart:232:15)
flutter: #71     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #72     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #73     RenderViewportBase._paintContents (package:flutter/src/rendering/viewport.dart:765:17)
flutter: #74     RenderViewportBase.paint (package:flutter/src/rendering/viewport.dart:750:7)
flutter: #75     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #76     PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #77     PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #78     PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #79     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #80     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #81     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #82     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #83     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #84     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #85     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #86     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #87     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #88     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #89     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #90     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #91     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #92     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #93     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #94     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #95     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #96     RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #97     PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #98     RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #99     RenderAnimatedOpacityMixin.paint (package:flutter/src/rendering/proxy_box.dart:1091:11)
flutter: #100    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #101    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #102    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #103    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #104    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #105    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #106    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #107    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #108    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #109    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #110    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #111    RenderCustomMultiChildLayoutBox.paint (package:flutter/src/rendering/custom_layout.dart:414:5)
flutter: #112    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #113    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #114    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #115    _RenderInkFeatures.paint (package:flutter/src/material/material.dart:621:11)
flutter: #116    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #117    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #118    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #119    RenderPhysicalModel.paint.<anonymous closure> (package:flutter/src/rendering/proxy_box.dart:2083:15)
flutter: #120    PaintingContext.pushClipRRect (package:flutter/src/rendering/object.dart:586:14)
flutter: #121    RenderPhysicalModel.paint (package:flutter/src/rendering/proxy_box.dart:2070:21)
flutter: #122    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #123    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #124    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #125    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #126    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #127    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #128    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #129    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #130    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #131    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #132    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #133    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #134    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #135    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #136    RenderBoxContainerDefaultsMixin.defaultPaint (package:flutter/src/rendering/box.dart:3171:15)
flutter: #137    RenderStack.paintStack (package:flutter/src/rendering/stack.dart:660:5)
flutter: #138    RenderStack.paint (package:flutter/src/rendering/stack.dart:676:7)
flutter: #139    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #140    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #141    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #142    RenderDecoratedBox.paint (package:flutter/src/rendering/proxy_box.dart:2341:11)
flutter: #143    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #144    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #145    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #146    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #147    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #148    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #149    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #150    RenderFractionalTranslation.paint (package:flutter/src/rendering/proxy_box.dart:2951:13)
flutter: #151    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #152    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #153    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #154    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #155    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #156    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #157    PaintingContext._compositeChild (package:flutter/src/rendering/object.dart:272:7)
flutter: #158    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:253:7)
flutter: #159    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #160    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #161    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #162    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #163    RenderOffstage.paint (package:flutter/src/rendering/proxy_box.dart:3742:11)
flutter: #164    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #165    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #166    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #167    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #168    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #169    _RenderTheaterMixin.paint (package:flutter/src/widgets/overlay.dart:1043:15)
flutter: #170    _RenderTheater.paint (package:flutter/src/widgets/overlay.dart:1372:13)
flutter: #171    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #172    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #173    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #174    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #175    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #176    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #177    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #178    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #179    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #180    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #181    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #182    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #183    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #184    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #185    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #186    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #187    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #188    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #189    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #190    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #191    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #192    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #193    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #194    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #195    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #196    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #197    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #198    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #199    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #200    RenderProxyBoxMixin.paint (package:flutter/src/rendering/proxy_box.dart:142:13)
flutter: #201    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #202    PaintingContext.paintChild (package:flutter/src/rendering/object.dart:261:13)
flutter: #203    RenderView.paint (package:flutter/src/rendering/view.dart:313:15)
flutter: #204    RenderObject._paintWithContext (package:flutter/src/rendering/object.dart:3287:7)
flutter: #205    PaintingContext._repaintCompositedChild (package:flutter/src/rendering/object.dart:177:11)
flutter: #206    PaintingContext.repaintCompositedChild (package:flutter/src/rendering/object.dart:120:5)
flutter: #207    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1193:31)
flutter: #208    PipelineOwner.flushPaint (package:flutter/src/rendering/object.dart:1203:15)
flutter: #209    RendererBinding.drawFrame (package:flutter/src/rendering/binding.dart:611:23)
flutter: #210    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1178:13)
flutter: #211    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #212    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #213    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #214    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #215    _invoke (dart:ui/hooks.dart:312:13)
flutter: #216    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #217    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:45.082191] [PerformanceMonitoringService] Slow frame detected {"duration_ms":383}
flutter: 🐛 DEBUG [2025-07-09T16:49:45.132207] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:45.182304] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:46.152066] [PerformanceMonitoringService] Slow frame detected {"duration_ms":136}
flutter: 🐛 DEBUG [2025-07-09T16:49:47.704789] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-07-09T16:49:47.733030] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-07-09T16:49:49.799106] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:49.848900] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.349040] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.399257] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.456166] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-09T16:49:52.513371] [HotelDetailsScreenEnhanced] Error initializing hotel details screen _HotelDetailsScreenEnhancedState is a SingleTickerProviderStateMixin but multiple tickers were created.
flutter: A SingleTickerProviderStateMixin can only be used as a TickerProvider once.
flutter: If a State is used for multiple AnimationController objects, or if it is passed to other objects and those objects might use it more than one time in total, then instead of mixing in a SingleTickerProviderStateMixin, use a regular TickerProviderStateMixin.
flutter: Stack trace:
flutter: #0      SingleTickerProviderStateMixin.createTicker.<anonymous closure> (package:flutter/src/widgets/ticker_provider.dart:190:7)
flutter: #1      SingleTickerProviderStateMixin.createTicker (package:flutter/src/widgets/ticker_provider.dart:199:6)
flutter: #2      new AnimationController (package:flutter/src/animation/animation_controller.dart:261:21)
flutter: #3      _HotelDetailsScreenEnhancedState.initState (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:64:30)
flutter: #4      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5762:55)
flutter: #5      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #6      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #7      Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #8      SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #9      Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #10     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #11     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #12     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #13     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #14     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #15     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #16     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #17     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #18     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #19     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #20     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #21     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #22     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #23     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #24     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #25     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #26     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #27     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #28     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #29     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #30     MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #31     MultiChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:7061:32)
flutter: #32     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #33     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #34     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #35     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #36     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #37     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #38     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #39     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #40     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #41     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #42     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #43     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #44     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #45     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #46     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #47     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #48     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #49     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #50     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #51     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #52     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #53     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #54     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #55     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #56     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #57     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #58     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #59     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #60     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #61     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #62     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #63     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #64     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #65     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #66     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #67     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #68     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #69     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #70     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #71     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #72     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #73     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #74     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #75     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #76     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #77     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #78     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #79     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #80     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #81     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #82     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #83     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #84     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #85     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #86     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #87     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #88     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #89     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #90     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #91     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #92     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #93     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #94     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #95     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #96     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #97     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #98     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #99     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #100    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #101    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #102    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #103    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #104    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #105    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #106    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #107    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #108    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #109    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #110    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #111    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #112    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #113    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #114    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #115    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #116    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #117    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #118    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #119    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #120    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #121    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #122    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #123    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #124    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #125    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #126    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #127    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #128    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #129    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #130    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #131    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #132    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #133    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #134    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #135    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #136    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #137    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #138    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #139    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #140    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #141    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #142    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #143    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #144    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #145    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #146    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #147    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #148    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #149    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #150    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #151    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #152    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #153    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #154    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #155    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #156    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #157    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #158    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #159    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #160    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #161    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #162    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #163    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #164    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #165    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #166    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #167    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #168    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #169    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #170    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #171    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #172    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #173    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #174    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #175    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #176    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #177    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #178    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #179    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #180    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #181    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #182    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #183    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #184    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #185    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #186    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #187    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #188    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #189    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #190    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #191    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #192    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #193    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #194    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #195    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #196    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #197    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #198    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #199    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #200    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #201    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #202    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #203    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #204    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #205    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #206    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #207    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #208    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #209    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #210    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #211    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #212    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #213    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #214    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #215    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #216    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #217    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #218    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #219    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #220    MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #221    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #222    Element.updateChildren (package:flutter/src/widgets/framework.dart:4150:32)
flutter: #223    MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #224    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #225    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #226    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #227    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #228    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #229    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #230    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #231    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #232    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #233    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #234    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #235    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #236    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #237    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #238    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #239    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #240    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #241    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #242    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #243    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #244    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #245    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #246    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #247    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #248    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #249    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #250    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #251    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #252    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #253    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #254    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #255    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #256    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #257    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #258    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #259    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #260    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #261    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #262    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #263    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #264    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #265    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #266    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #267    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #268    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #269    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #270    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #271    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #272    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #273    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #274    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #275    BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #276    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #277    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #278    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #279    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #280    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #281    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #282    _invoke (dart:ui/hooks.dart:312:13)
flutter: #283    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #284    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-07-09T16:49:52.734906] [FlutterError] LateInitializationError: Field '_fadeAnimation@*********' has not been initialized. LateInitializationError: Field '_fadeAnimation@*********' has not been initialized.
flutter: Stack trace:
flutter: #0      _HotelDetailsScreenEnhancedState._fadeAnimation (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart)
flutter: #1      _HotelDetailsScreenEnhancedState.build (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:184:18)
flutter: #2      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #3      ConsumerStatefulElement.build (package:flutter_riverpod/src/consumer.dart:539:20)
flutter: #4      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #5      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #6      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #7      ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #8      StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #9      ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #10     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #11     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #12     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #13     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #14     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #15     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #16     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #17     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #18     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #19     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #20     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #21     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #22     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #23     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #24     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #25     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #26     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #27     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #28     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #29     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #30     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #31     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #32     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #33     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #34     MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #35     MultiChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:7061:32)
flutter: #36     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #37     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #38     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #39     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #40     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #41     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #42     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #43     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #44     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #45     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #46     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #47     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #48     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #49     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #50     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #51     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #52     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #53     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #54     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #55     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #56     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #57     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #58     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #59     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #60     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #61     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #62     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #63     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #64     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #65     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #66     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #67     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #68     SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #69     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #70     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #71     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #72     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #73     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #74     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #75     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #76     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #77     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #78     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #79     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #80     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #81     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #82     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #83     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #84     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #85     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #86     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #87     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #88     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #89     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #90     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #91     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #92     ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #93     Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #94     Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #95     ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #96     StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #97     Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #98     ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #99     StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #100    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #101    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #102    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #103    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #104    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #105    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #106    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #107    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #108    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #109    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #110    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #111    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #112    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #113    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #114    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #115    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #116    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #117    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #118    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #119    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #120    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #121    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #122    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #123    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #124    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #125    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #126    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #127    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #128    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #129    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #130    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #131    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #132    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #133    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #134    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #135    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #136    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #137    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #138    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #139    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #140    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #141    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #142    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #143    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #144    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #145    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #146    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #147    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #148    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #149    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #150    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #151    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #152    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #153    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #154    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #155    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #156    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #157    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #158    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #159    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #160    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #161    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #162    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #163    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #164    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #165    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #166    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #167    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #168    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #169    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #170    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #171    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #172    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #173    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #174    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #175    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #176    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #177    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #178    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #179    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #180    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #181    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #182    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #183    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #184    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #185    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #186    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #187    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #188    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #189    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #190    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #191    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #192    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #193    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #194    SingleChildRenderObjectElement.mount (package:flutter/src/widgets/framework.dart:6914:14)
flutter: #195    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #196    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #197    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #198    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #199    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #200    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #201    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #202    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #203    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #204    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #205    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #206    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #207    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #208    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #209    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #210    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #211    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #212    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #213    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #214    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #215    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #216    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #217    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #218    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #219    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #220    ComponentElement._firstBuild (package:flutter/src/widgets/framework.dart:5613:5)
flutter: #221    StatefulElement._firstBuild (package:flutter/src/widgets/framework.dart:5785:11)
flutter: #222    ComponentElement.mount (package:flutter/src/widgets/framework.dart:5607:5)
flutter: #223    Element.inflateWidget (package:flutter/src/widgets/framework.dart:4480:16)
flutter: #224    MultiChildRenderObjectElement.inflateWidget (package:flutter/src/widgets/framework.dart:7049:36)
flutter: #225    Element.updateChild (package:flutter/src/widgets/framework.dart:3963:18)
flutter: #226    Element.updateChildren (package:flutter/src/widgets/framework.dart:4150:32)
flutter: #227    MultiChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:7074:17)
flutter: #228    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #229    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #230    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #231    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #232    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #233    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #234    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #235    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #236    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #237    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #238    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #239    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #240    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #241    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #242    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #243    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #244    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #245    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #246    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #247    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #248    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #249    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #250    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #251    _InheritedNotifierElement.update (package:flutter/src/widgets/inherited_notifier.dart:112:11)
flutter: #252    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #253    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #254    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #255    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #256    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #257    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #258    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #259    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #260    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #261    StatefulElement.update (package:flutter/src/widgets/framework.dart:5817:5)
flutter: #262    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #263    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #264    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #265    SingleChildRenderObjectElement.update (package:flutter/src/widgets/framework.dart:6921:14)
flutter: #266    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #267    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #268    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #269    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #270    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #271    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #272    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #273    ProxyElement.update (package:flutter/src/widgets/framework.dart:5960:5)
flutter: #274    Element.updateChild (package:flutter/src/widgets/framework.dart:3941:15)
flutter: #275    ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5656:16)
flutter: #276    StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #277    Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #278    BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #279    BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #280    BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #281    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #282    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #283    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #284    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #285    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #286    _invoke (dart:ui/hooks.dart:312:13)
flutter: #287    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #288    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:52.774699] [PerformanceMonitoringService] Slow frame detected {"duration_ms":326}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.866576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":90}
flutter: 🐛 DEBUG [2025-07-09T16:49:52.932580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ❌ ERROR [2025-07-09T16:49:53.142103] [FlutterError] HTTP request failed, statusCode: 404, https://example.com/hotels/lagos_luxury.jpg HTTP request failed, statusCode: 404, https://example.com/hotels/lagos_luxury.jpg
flutter: Stack trace:
flutter: #0      NetworkImage._loadAsync (package:flutter/src/painting/_network_image_io.dart:115:9)
flutter: <asynchronous suspension>
flutter: #1      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1013:3)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:49:53.867344] [PerformanceMonitoringService] Slow frame detected {"duration_ms":185}
flutter: 🐛 DEBUG [2025-07-09T16:49:55.045580] [PerformanceMonitoringService] Slow frame detected {"duration_ms":179}
flutter: 🐛 DEBUG [2025-07-09T16:49:55.065591] [PerformanceMonitoringService] Slow frame detected {"duration_ms":20}
flutter: 🐛 DEBUG [2025-07-09T16:49:56.196354] [PerformanceMonitoringService] Slow frame detected {"duration_ms":163}
flutter: 🐛 DEBUG [2025-07-09T16:49:56.215520] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:49:57.222444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":40}
flutter: 🐛 DEBUG [2025-07-09T16:49:57.249441] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: ⚠️ WARNING [2025-07-09T16:49:58.912111] [PerformanceMonitoringService] High memory usage detected {"memory_mb":161.0}
flutter: 🐛 DEBUG [2025-07-09T16:50:00.812176] [PerformanceMonitoringService] Slow frame detected {"duration_ms":97}
flutter: 🐛 DEBUG [2025-07-09T16:50:00.832231] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:50:04.665326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.182038] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.716368] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.816319] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:05.866284] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:06.648594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:06.783506] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:50:11.965492] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:12.201727] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:50:13.916641] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:13.998747] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-07-09T16:50:15.923928] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-07-09T16:50:15.949044] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-07-09T16:50:17.530598] [PerformanceMonitoringService] Slow frame detected {"duration_ms":47}
flutter: 🐛 DEBUG [2025-07-09T16:50:17.557020] [PerformanceMonitoringService] Slow frame detected {"duration_ms":19}
flutter: 🐛 DEBUG [2025-07-09T16:50:26.365594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:50:28.910852] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-07-09T16:50:29.615371] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-07-09T16:50:30.948910] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:31.848531] [PerformanceMonitoringService] Slow frame detected {"duration_ms":49}
flutter: ⚠️ WARNING [2025-07-09T16:50:43.909376] [PerformanceMonitoringService] High memory usage detected {"memory_mb":159.0}
flutter: 🐛 DEBUG [2025-07-09T16:50:48.302385] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:50:58.015523] [PerformanceMonitoringService] Slow frame detected {"duration_ms":233}
flutter: ⚠️ WARNING [2025-07-09T16:50:58.911406] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-07-09T16:51:01.215505] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:51:02.949423] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:51:03.449021] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:51:06.565091] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:51:13.915356] [PerformanceMonitoringService] High memory usage detected {"memory_mb":164.0}
flutter: 🐛 DEBUG [2025-07-09T16:51:15.232891] [PerformanceMonitoringService] Slow frame detected {"duration_ms":17}
flutter: ⚠️ WARNING [2025-07-09T16:51:28.911275] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-07-09T16:51:33.963611] [PerformanceMonitoringService] Slow frame detected {"duration_ms":48}
flutter: 🐛 DEBUG [2025-07-09T16:51:33.982042] [PerformanceMonitoringService] Slow frame detected {"duration_ms":18}
flutter: ⚠️ WARNING [2025-07-09T16:51:43.913154] [PerformanceMonitoringService] High memory usage detected {"memory_mb":162.0}
flutter: 🐛 DEBUG [2025-07-09T16:51:48.748813] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:51:49.016567] [PerformanceMonitoringService] Slow frame detected {"duration_ms":67}
flutter: 🐛 DEBUG [2025-07-09T16:51:50.531923] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:51:53.665249] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-07-09T16:51:58.913556] [PerformanceMonitoringService] High memory usage detected {"memory_mb":163.0}
flutter: 🐛 DEBUG [2025-07-09T16:51:59.565254] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-07-09T16:52:04.681792] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:52:07.816147] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-07-09T16:52:12.016195] [PerformanceMonitoringService] Slow frame detected {"duration_ms":51}
flutter: ⚠️ WARNING [2025-07-09T16:52:13.910876] [PerformanceMonitoringService] High memory usage detected {"memory_mb":160.0}
flutter: 🐛 DEBUG [2025-07-09T16:52:17.699555] [PerformanceMonitoringService] Slow frame detected {"duration_ms":566}
flutter: 🐛 DEBUG [2025-07-09T16:52:17.991837] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-07-09T16:52:19.292439] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1294}
flutter: 🐛 DEBUG [2025-07-09T16:52:19.882135] [PerformanceMonitoringService] Slow frame detected {"duration_ms":589}
flutter: 🐛 DEBUG [2025-07-09T16:52:21.014980] [PerformanceMonitoringService] Slow frame detected {"duration_ms":1133}
flutter: ❌ ERROR [2025-07-09T16:52:21.070699] [FlutterError] LateInitializationError: Field '_fadeAnimation@*********' has not been initialized. LateInitializationError: Field '_fadeAnimation@*********' has not been initialized.
flutter: Stack trace:
flutter: #0      _HotelDetailsScreenEnhancedState._fadeAnimation (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart)
flutter: #1      _HotelDetailsScreenEnhancedState.build (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:184:18)
flutter: #2      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #3      ConsumerStatefulElement.build (package:flutter_riverpod/src/consumer.dart:539:20)
flutter: #4      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #5      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #6      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #7      BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #8      BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #9      BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #10     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #11     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #12     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #13     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #14     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #15     _invoke (dart:ui/hooks.dart:312:13)
flutter: #16     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #17     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: 🐛 DEBUG [2025-07-09T16:52:26.857334] [PerformanceMonitoringService] Slow frame detected {"duration_ms":5839}
flutter: ❌ ERROR [2025-07-09T16:52:28.150421] [FlutterError] LateInitializationError: Field '_fadeAnimation@*********' has not been initialized. LateInitializationError: Field '_fadeAnimation@*********' has not been initialized.
flutter: Stack trace:
flutter: #0      _HotelDetailsScreenEnhancedState._fadeAnimation (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart)
flutter: #1      _HotelDetailsScreenEnhancedState.build (package:culture_connect/screens/travel/hotel/hotel_details_screen_enhanced.dart:184:18)
flutter: #2      StatefulElement.build (package:flutter/src/widgets/framework.dart:5743:27)
flutter: #3      ConsumerStatefulElement.build (package:flutter_riverpod/src/consumer.dart:539:20)
flutter: #4      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:5631:15)
flutter: #5      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5794:11)
flutter: #6      Element.rebuild (package:flutter/src/widgets/framework.dart:5347:7)
flutter: #7      BuildScope._tryRebuild (package:flutter/src/widgets/framework.dart:2694:15)
flutter: #8      BuildScope._flushDirtyElements (package:flutter/src/widgets/framework.dart:2753:11)
flutter: #9      BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:3048:18)
flutter: #10     WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1176:21)
flutter: #11     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #12     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #13     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #14     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #15     _invoke (dart:ui/hooks.dart:312:13)
flutter: #16     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #17     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
Application finished.
the Dart compiler exited unexpectedly.
(base) Macs-MacBook-Pro:culture_connect mac$ 